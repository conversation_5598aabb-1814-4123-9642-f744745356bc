{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/src/hooks/useClientMount.ts"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\n/**\n * Custom hook to handle client-side mounting and prevent hydration mismatches\n * Returns true only after the component has mounted on the client side\n */\nexport function useClientMount() {\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  return mounted;\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAQO,SAAS;;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,WAAW;QACb;mCAAG,EAAE;IAEL,OAAO;AACT;GARgB"}}, {"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useEffect, useState } from 'react';\nimport { useClientMount } from '../hooks/useClientMount';\n\nexport default function Hero() {\n  const mounted = useClientMount();\n  const [mousePosition, setMousePosition] = useState({ x: 0.5, y: 0.5 });\n\n  useEffect(() => {\n    if (!mounted) return;\n\n    const handleMouseMove = (e: MouseEvent) => {\n      setMousePosition({\n        x: e.clientX / window.innerWidth,\n        y: e.clientY / window.innerHeight,\n      });\n    };\n\n    window.addEventListener('mousemove', handleMouseMove);\n    return () => window.removeEventListener('mousemove', handleMouseMove);\n  }, [mounted]);\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      <div className=\"container mx-auto px-6 py-24 relative z-10\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"text-center max-w-4xl mx-auto\"\n          style={mounted ? {\n            transform: `translate(\n              ${(mousePosition.x - 0.5) * 20}px,\n              ${(mousePosition.y - 0.5) * 20}px\n            )`,\n          } : undefined}\n        >\n          <h1 className=\"text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-blue-400 to-purple-600 bg-clip-text text-transparent\">\n            Next-Gen AI Solutions\n          </h1>\n          <p className=\"text-xl md:text-2xl text-gray-300 mb-12\">\n            Harness the power of artificial intelligence to transform your ideas into reality\n          </p>\n          <motion.div\n            className=\"flex flex-col md:flex-row gap-4 justify-center\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3, duration: 0.8 }}\n          >\n            <motion.button\n              className=\"px-8 py-4 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium text-lg\"\n              whileHover={{ scale: 1.05, boxShadow: '0 0 20px rgba(79, 70, 229, 0.4)' }}\n              whileTap={{ scale: 0.95 }}\n            >\n              Get Started\n            </motion.button>\n            <motion.button\n              className=\"px-8 py-4 rounded-full border-2 border-gray-500 text-white font-medium text-lg hover:border-gray-300\"\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              Learn More\n            </motion.button>\n          </motion.div>\n        </motion.div>\n      </div>\n\n      {/* Decorative elements */}\n      <div className=\"absolute inset-0 z-0\">\n        {mounted && (\n          <>\n            <motion.div\n              className=\"absolute w-96 h-96 bg-blue-500/20 rounded-full filter blur-3xl\"\n              animate={{\n                x: mousePosition.x * 100,\n                y: mousePosition.y * 100,\n                scale: [1, 1.1, 1],\n              }}\n              transition={{ duration: 0.8 }}\n              style={{\n                left: '20%',\n                top: '20%',\n              }}\n            />\n            <motion.div\n              className=\"absolute w-96 h-96 bg-purple-500/20 rounded-full filter blur-3xl\"\n              animate={{\n                x: -mousePosition.x * 100,\n                y: -mousePosition.y * 100,\n                scale: [1, 1.2, 1],\n              }}\n              transition={{ duration: 0.8 }}\n              style={{\n                right: '20%',\n                bottom: '20%',\n              }}\n            />\n          </>\n        )}\n      </div>\n    </section>\n  );\n}"], "names": [], "mappings": ";;;;AAGA;AACA;AAFA;;;AAFA;;;;AAMe,SAAS;;IACtB,MAAM,UAAU,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAC7B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAK,GAAG;IAAI;IAEpE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,CAAC,SAAS;YAEd,MAAM;kDAAkB,CAAC;oBACvB,iBAAiB;wBACf,GAAG,EAAE,OAAO,GAAG,OAAO,UAAU;wBAChC,GAAG,EAAE,OAAO,GAAG,OAAO,WAAW;oBACnC;gBACF;;YAEA,OAAO,gBAAgB,CAAC,aAAa;YACrC;kCAAO,IAAM,OAAO,mBAAmB,CAAC,aAAa;;QACvD;yBAAG;QAAC;KAAQ;IAEZ,qBACE,6LAAC;QAAQ,WAAU;;0BACjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;oBACV,OAAO,UAAU;wBACf,WAAW,CAAC;cACV,EAAE,CAAC,cAAc,CAAC,GAAG,GAAG,IAAI,GAAG;cAC/B,EAAE,CAAC,cAAc,CAAC,GAAG,GAAG,IAAI,GAAG;aAChC,CAAC;oBACJ,IAAI;;sCAEJ,6LAAC;4BAAG,WAAU;sCAAiH;;;;;;sCAG/H,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;sCAGvD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;;8CAExC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,WAAU;oCACV,YAAY;wCAAE,OAAO;wCAAM,WAAW;oCAAkC;oCACxE,UAAU;wCAAE,OAAO;oCAAK;8CACzB;;;;;;8CAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;8CACzB;;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAI,WAAU;0BACZ,yBACC;;sCACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCACP,GAAG,cAAc,CAAC,GAAG;gCACrB,GAAG,cAAc,CAAC,GAAG;gCACrB,OAAO;oCAAC;oCAAG;oCAAK;iCAAE;4BACpB;4BACA,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,OAAO;gCACL,MAAM;gCACN,KAAK;4BACP;;;;;;sCAEF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCACP,GAAG,CAAC,cAAc,CAAC,GAAG;gCACtB,GAAG,CAAC,cAAc,CAAC,GAAG;gCACtB,OAAO;oCAAC;oCAAG;oCAAK;iCAAE;4BACpB;4BACA,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,OAAO;gCACL,OAAO;gCACP,QAAQ;4BACV;;;;;;;;;;;;;;;;;;;AAOd;GAlGwB;;QACN,iIAAA,CAAA,iBAAc;;;KADR"}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}