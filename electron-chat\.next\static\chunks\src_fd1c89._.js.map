{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/src/hooks/useClientMount.ts"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\n/**\n * Custom hook to handle client-side mounting and prevent hydration mismatches\n * Returns true only after the component has mounted on the client side\n */\nexport function useClientMount() {\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  return mounted;\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAQO,SAAS;;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,WAAW;QACb;mCAAG,EAAE;IAEL,OAAO;AACT;GARgB"}}, {"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { motion, useScroll, useTransform } from 'framer-motion';\nimport { useEffect, useState } from 'react';\nimport { useClientMount } from '../hooks/useClientMount';\n\nexport default function Hero() {\n  const mounted = useClientMount();\n  const [mousePosition, setMousePosition] = useState({ x: 0.5, y: 0.5 });\n  const { scrollY } = useScroll();\n  const y = useTransform(scrollY, [0, 500], [0, 150]);\n  const opacity = useTransform(scrollY, [0, 300], [1, 0]);\n\n  useEffect(() => {\n    if (!mounted) return;\n\n    const handleMouseMove = (e: MouseEvent) => {\n      setMousePosition({\n        x: e.clientX / window.innerWidth,\n        y: e.clientY / window.innerHeight,\n      });\n    };\n\n    window.addEventListener('mousemove', handleMouseMove);\n    return () => window.removeEventListener('mousemove', handleMouseMove);\n  }, [mounted]);\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2,\n        delayChildren: 0.3,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.8,\n        ease: [0.6, -0.05, 0.01, 0.99],\n      },\n    },\n  };\n\n  return (\n    <section className=\"relative h-screen flex items-center justify-center overflow-hidden pt-16\">\n      <motion.div\n        className=\"container mx-auto px-6 py-12 relative z-10\"\n        style={{ y, opacity }}\n      >\n        <motion.div\n          className=\"text-center max-w-4xl mx-auto\"\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n          style={mounted ? {\n            transform: `translate(\n              ${(mousePosition.x - 0.5) * 10}px,\n              ${(mousePosition.y - 0.5) * 10}px\n            )`,\n          } : undefined}\n        >\n          {/* Badge */}\n          <motion.div\n            variants={itemVariants}\n            className=\"inline-flex items-center space-x-2 px-3 py-1.5 rounded-full glass mb-6 text-xs font-medium\"\n          >\n            <span className=\"w-1.5 h-1.5 bg-green-400 rounded-full animate-pulse\"></span>\n            <span className=\"gradient-text-accent\">AI-Powered Innovation</span>\n          </motion.div>\n\n          {/* Main Heading */}\n          <motion.h1\n            variants={itemVariants}\n            className=\"text-4xl md:text-6xl lg:text-7xl font-black mb-6 leading-tight\"\n          >\n            <span className=\"gradient-text-primary block\">Next-Gen</span>\n            <span className=\"gradient-text-secondary block\">AI Solutions</span>\n          </motion.h1>\n\n          {/* Subtitle */}\n          <motion.p\n            variants={itemVariants}\n            className=\"text-lg md:text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed font-light\"\n          >\n            Harness the power of artificial intelligence to{' '}\n            <span className=\"gradient-text-accent font-medium\">transform your ideas</span>{' '}\n            into reality with cutting-edge technology\n          </motion.p>\n\n          {/* Stats */}\n          <motion.div\n            variants={itemVariants}\n            className=\"flex flex-wrap justify-center gap-6 mb-8\"\n          >\n            {[\n              { number: '99.9%', label: 'Accuracy' },\n              { number: '10x', label: 'Faster' },\n              { number: '24/7', label: 'Available' },\n            ].map((stat, index) => (\n              <motion.div\n                key={stat.label}\n                className=\"text-center\"\n                whileHover={{ scale: 1.05 }}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.8 + index * 0.1 }}\n              >\n                <div className=\"text-2xl md:text-3xl font-bold gradient-text-primary\">\n                  {stat.number}\n                </div>\n                <div className=\"text-gray-400 text-xs uppercase tracking-wider\">\n                  {stat.label}\n                </div>\n              </motion.div>\n            ))}\n          </motion.div>\n\n          {/* CTA Buttons */}\n          <motion.div\n            variants={itemVariants}\n            className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\"\n          >\n            <motion.button\n              className=\"group relative px-6 py-3 rounded-xl btn-primary font-semibold text-base shadow-lg hover:shadow-purple-500/25 transition-all duration-300 overflow-hidden\"\n              whileHover={{ scale: 1.05, y: -2 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <span className=\"relative z-10 flex items-center space-x-2\">\n                <span>Start Building</span>\n                <motion.svg\n                  className=\"w-4 h-4\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                  whileHover={{ x: 3 }}\n                  transition={{ duration: 0.2 }}\n                >\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 7l5 5m0 0l-5 5m5-5H6\" />\n                </motion.svg>\n              </span>\n            </motion.button>\n\n            <motion.button\n              className=\"group px-6 py-3 rounded-xl btn-secondary font-semibold text-base flex items-center space-x-2\"\n              whileHover={{ scale: 1.05, y: -2 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n              <span>Watch Demo</span>\n            </motion.button>\n          </motion.div>\n\n          {/* Scroll Indicator */}\n          <motion.div\n            className=\"absolute bottom-6 left-1/2 transform -translate-x-1/2\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 1.5 }}\n          >\n            <motion.div\n              className=\"w-5 h-8 border-2 border-gray-400 rounded-full flex justify-center\"\n              animate={{ y: [0, 8, 0] }}\n              transition={{ duration: 2, repeat: Infinity }}\n            >\n              <motion.div\n                className=\"w-0.5 h-2 bg-gradient-to-b from-blue-400 to-purple-600 rounded-full mt-1.5\"\n                animate={{ scaleY: [1, 0.5, 1] }}\n                transition={{ duration: 2, repeat: Infinity }}\n              />\n            </motion.div>\n          </motion.div>\n        </motion.div>\n      </motion.div>\n\n      {/* Enhanced Decorative elements */}\n      <div className=\"absolute inset-0 z-0\">\n        {mounted && (\n          <>\n            {/* Interactive orbs */}\n            <motion.div\n              className=\"absolute w-96 h-96 rounded-full\"\n              style={{\n                background: 'radial-gradient(circle, rgba(102, 126, 234, 0.3) 0%, rgba(102, 126, 234, 0.1) 50%, transparent 100%)',\n                filter: 'blur(40px)',\n                left: '10%',\n                top: '20%',\n              }}\n              animate={{\n                x: mousePosition.x * 50,\n                y: mousePosition.y * 50,\n                scale: [1, 1.2, 1],\n              }}\n              transition={{ duration: 2, ease: 'easeInOut' }}\n            />\n\n            <motion.div\n              className=\"absolute w-80 h-80 rounded-full\"\n              style={{\n                background: 'radial-gradient(circle, rgba(118, 75, 162, 0.4) 0%, rgba(118, 75, 162, 0.1) 50%, transparent 100%)',\n                filter: 'blur(30px)',\n                right: '15%',\n                bottom: '25%',\n              }}\n              animate={{\n                x: -mousePosition.x * 40,\n                y: -mousePosition.y * 40,\n                scale: [1, 0.8, 1],\n              }}\n              transition={{ duration: 3, ease: 'easeInOut' }}\n            />\n\n            {/* Floating geometric shapes */}\n            {Array.from({ length: 6 }, (_, i) => (\n              <motion.div\n                key={i}\n                className=\"absolute w-4 h-4 border border-purple-400/30\"\n                style={{\n                  left: `${20 + i * 15}%`,\n                  top: `${30 + (i % 2) * 40}%`,\n                  clipPath: i % 2 === 0 ? 'polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)' : 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)',\n                }}\n                animate={{\n                  y: [0, -20, 0],\n                  rotate: [0, 180, 360],\n                  opacity: [0.3, 0.7, 0.3],\n                }}\n                transition={{\n                  duration: 4 + i,\n                  repeat: Infinity,\n                  delay: i * 0.5,\n                }}\n              />\n            ))}\n          </>\n        )}\n      </div>\n    </section>\n  );\n}"], "names": [], "mappings": ";;;;AAGA;AACA;AAFA;AAAA;AAAA;;;AAFA;;;;AAMe,SAAS;;IACtB,MAAM,UAAU,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAC7B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAK,GAAG;IAAI;IACpE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD;IAC5B,MAAM,IAAI,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE,SAAS;QAAC;QAAG;KAAI,EAAE;QAAC;QAAG;KAAI;IAClD,MAAM,UAAU,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE,SAAS;QAAC;QAAG;KAAI,EAAE;QAAC;QAAG;KAAE;IAEtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,CAAC,SAAS;YAEd,MAAM;kDAAkB,CAAC;oBACvB,iBAAiB;wBACf,GAAG,EAAE,OAAO,GAAG,OAAO,UAAU;wBAChC,GAAG,EAAE,OAAO,GAAG,OAAO,WAAW;oBACnC;gBACF;;YAEA,OAAO,gBAAgB,CAAC,aAAa;YACrC;kCAAO,IAAM,OAAO,mBAAmB,CAAC,aAAa;;QACvD;yBAAG;QAAC;KAAQ;IAEZ,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;oBAAC;oBAAK,CAAC;oBAAM;oBAAM;iBAAK;YAChC;QACF;IACF;IAEA,qBACE,6LAAC;QAAQ,WAAU;;0BACjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBAAE;oBAAG;gBAAQ;0BAEpB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU;oBACV,SAAQ;oBACR,SAAQ;oBACR,OAAO,UAAU;wBACf,WAAW,CAAC;cACV,EAAE,CAAC,cAAc,CAAC,GAAG,GAAG,IAAI,GAAG;cAC/B,EAAE,CAAC,cAAc,CAAC,GAAG,GAAG,IAAI,GAAG;aAChC,CAAC;oBACJ,IAAI;;sCAGJ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;;8CAEV,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;oCAAK,WAAU;8CAAuB;;;;;;;;;;;;sCAIzC,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,WAAU;;8CAEV,6LAAC;oCAAK,WAAU;8CAA8B;;;;;;8CAC9C,6LAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;;sCAIlD,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,UAAU;4BACV,WAAU;;gCACX;gCACiD;8CAChD,6LAAC;oCAAK,WAAU;8CAAmC;;;;;;gCAA4B;gCAAI;;;;;;;sCAKrF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;sCAET;gCACC;oCAAE,QAAQ;oCAAS,OAAO;gCAAW;gCACrC;oCAAE,QAAQ;oCAAO,OAAO;gCAAS;gCACjC;oCAAE,QAAQ;oCAAQ,OAAO;gCAAY;6BACtC,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,MAAM,QAAQ;oCAAI;;sDAEvC,6LAAC;4CAAI,WAAU;sDACZ,KAAK,MAAM;;;;;;sDAEd,6LAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK;;;;;;;mCAXR,KAAK,KAAK;;;;;;;;;;sCAkBrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;;8CAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,WAAU;oCACV,YAAY;wCAAE,OAAO;wCAAM,GAAG,CAAC;oCAAE;oCACjC,UAAU;wCAAE,OAAO;oCAAK;8CAExB,cAAA,6LAAC;wCAAK,WAAU;;0DACd,6LAAC;0DAAK;;;;;;0DACN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,MAAK;gDACL,QAAO;gDACP,SAAQ;gDACR,YAAY;oDAAE,GAAG;gDAAE;gDACnB,YAAY;oDAAE,UAAU;gDAAI;0DAE5B,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;8CAK3E,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,WAAU;oCACV,YAAY;wCAAE,OAAO;wCAAM,GAAG,CAAC;oCAAE;oCACjC,UAAU;wCAAE,OAAO;oCAAK;;sDAExB,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;sDAEvE,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAKV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;sCAEzB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,GAAG;wCAAC;wCAAG;wCAAG;qCAAE;gCAAC;gCACxB,YAAY;oCAAE,UAAU;oCAAG,QAAQ;gCAAS;0CAE5C,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,QAAQ;4CAAC;4CAAG;4CAAK;yCAAE;oCAAC;oCAC/B,YAAY;wCAAE,UAAU;wCAAG,QAAQ;oCAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQtD,6LAAC;gBAAI,WAAU;0BACZ,yBACC;;sCAEE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,OAAO;gCACL,YAAY;gCACZ,QAAQ;gCACR,MAAM;gCACN,KAAK;4BACP;4BACA,SAAS;gCACP,GAAG,cAAc,CAAC,GAAG;gCACrB,GAAG,cAAc,CAAC,GAAG;gCACrB,OAAO;oCAAC;oCAAG;oCAAK;iCAAE;4BACpB;4BACA,YAAY;gCAAE,UAAU;gCAAG,MAAM;4BAAY;;;;;;sCAG/C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,OAAO;gCACL,YAAY;gCACZ,QAAQ;gCACR,OAAO;gCACP,QAAQ;4BACV;4BACA,SAAS;gCACP,GAAG,CAAC,cAAc,CAAC,GAAG;gCACtB,GAAG,CAAC,cAAc,CAAC,GAAG;gCACtB,OAAO;oCAAC;oCAAG;oCAAK;iCAAE;4BACpB;4BACA,YAAY;gCAAE,UAAU;gCAAG,MAAM;4BAAY;;;;;;wBAI9C,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,CAAC,GAAG,kBAC7B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAU;gCACV,OAAO;oCACL,MAAM,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC;oCACvB,KAAK,GAAG,KAAK,AAAC,IAAI,IAAK,GAAG,CAAC,CAAC;oCAC5B,UAAU,IAAI,MAAM,IAAI,gDAAgD;gCAC1E;gCACA,SAAS;oCACP,GAAG;wCAAC;wCAAG,CAAC;wCAAI;qCAAE;oCACd,QAAQ;wCAAC;wCAAG;wCAAK;qCAAI;oCACrB,SAAS;wCAAC;wCAAK;wCAAK;qCAAI;gCAC1B;gCACA,YAAY;oCACV,UAAU,IAAI;oCACd,QAAQ;oCACR,OAAO,IAAI;gCACb;+BAhBK;;;;;;;;;;;;;;;;;;AAwBrB;GAjPwB;;QACN,iIAAA,CAAA,iBAAc;QAEV,4KAAA,CAAA,YAAS;QACnB,+KAAA,CAAA,eAAY;QACN,+KAAA,CAAA,eAAY;;;KALN"}}, {"offset": {"line": 569, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}