'use client';

import { motion } from 'framer-motion';
import { useClientMount } from '../hooks/useClientMount';

interface FloatingElement {
  id: number;
  x: number;
  y: number;
  size: number;
  color: string;
  type: 'circle' | 'hexagon' | 'triangle';
  opacity: number;
  duration: number;
  delay: number;
}

export default function Background() {
  const mounted = useClientMount();

  const floatingElements: FloatingElement[] = [
    { id: 1, x: 15, y: 20, size: 80, color: '#667eea', type: 'circle', opacity: 0.1, duration: 12, delay: 0 },
    { id: 2, x: 75, y: 15, size: 60, color: '#764ba2', type: 'circle', opacity: 0.08, duration: 15, delay: 5 },
    { id: 3, x: 85, y: 70, size: 70, color: '#f093fb', type: 'circle', opacity: 0.06, duration: 18, delay: 10 },
  ];

  const gridLines = Array.from({ length: 20 }, (_, i) => i);

  if (!mounted) {
    return (
      <div className="fixed inset-0 -z-10 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900/20 to-slate-900" />
      </div>
    );
  }

  return (
    <div className="fixed inset-0 -z-10 overflow-hidden">
      {/* Animated gradient background */}
      <motion.div
        className="absolute inset-0"
        style={{
          background: 'radial-gradient(ellipse at top, #1a1a2e 0%, #16213e 50%, #0f0f23 100%)',
        }}
        animate={{
          background: [
            'radial-gradient(ellipse at top, #1a1a2e 0%, #16213e 50%, #0f0f23 100%)',
            'radial-gradient(ellipse at bottom right, #1a1a2e 0%, #16213e 50%, #0f0f23 100%)',
            'radial-gradient(ellipse at top left, #1a1a2e 0%, #16213e 50%, #0f0f23 100%)',
            'radial-gradient(ellipse at top, #1a1a2e 0%, #16213e 50%, #0f0f23 100%)',
          ],
        }}
        transition={{
          duration: 30,
          repeat: Infinity,
          ease: 'linear',
        }}
      />

      {/* Grid pattern */}
      <div className="absolute inset-0 opacity-[0.03]">
        <svg width="100%" height="100%" className="absolute inset-0">
          <defs>
            <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
              <path d="M 50 0 L 0 0 0 50" fill="none" stroke="currentColor" strokeWidth="1"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
        </svg>
      </div>

      {/* Floating elements */}
      {floatingElements.map((element) => (
        <motion.div
          key={element.id}
          className="absolute"
          initial={{
            x: `${element.x}%`,
            y: `${element.y}%`,
            opacity: 0,
            scale: 0.5,
          }}
          animate={{
            opacity: [element.opacity * 0.5, element.opacity, element.opacity * 0.5],
            scale: [0.9, 1, 0.9],
          }}
          transition={{
            duration: element.duration,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: element.delay,
          }}
          style={{
            width: element.size,
            height: element.size,
          }}
        >
          <div
            className="w-full h-full rounded-full"
            style={{
              background: `radial-gradient(circle at center, ${element.color}30, transparent)`,
              filter: 'blur(30px)',
            }}
          />
        </motion.div>
      ))}



      {/* Static ambient light effects */}
      <div
        className="absolute top-0 left-1/4 w-96 h-96 rounded-full opacity-20"
        style={{
          background: 'radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%)',
          filter: 'blur(60px)',
        }}
      />

      <div
        className="absolute bottom-0 right-1/4 w-80 h-80 rounded-full opacity-30"
        style={{
          background: 'radial-gradient(circle, rgba(118, 75, 162, 0.15) 0%, transparent 70%)',
          filter: 'blur(50px)',
        }}
      />
    </div>
  );
}