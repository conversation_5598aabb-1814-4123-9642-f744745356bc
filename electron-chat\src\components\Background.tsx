'use client';

import { motion } from 'framer-motion';
import { useClientMount } from '../hooks/useClientMount';

interface FloatingElement {
  id: number;
  x: number;
  y: number;
  size: number;
  color: string;
  type: 'circle' | 'hexagon' | 'triangle';
  opacity: number;
  duration: number;
  delay: number;
}

export default function Background() {
  const mounted = useClientMount();

  const floatingElements: FloatingElement[] = [
    { id: 1, x: 15, y: 20, size: 80, color: '#007AFF', type: 'circle', opacity: 0.08, duration: 12, delay: 0 },
    { id: 2, x: 75, y: 15, size: 60, color: '#5AC8FA', type: 'circle', opacity: 0.06, duration: 15, delay: 5 },
    { id: 3, x: 85, y: 70, size: 70, color: '#34C759', type: 'circle', opacity: 0.05, duration: 18, delay: 10 },
  ];

  const gridLines = Array.from({ length: 20 }, (_, i) => i);

  if (!mounted) {
    return (
      <div className="fixed inset-0 -z-10 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900/20 to-slate-900" />
      </div>
    );
  }

  return (
    <div className="fixed inset-0 -z-10 overflow-hidden">
      {/* iOS-style gradient background */}
      <div
        className="absolute inset-0"
        style={{
          background: 'linear-gradient(180deg, #000000 0%, #1C1C1E 100%)',
        }}
      />



      {/* Static floating elements */}
      {floatingElements.map((element) => (
        <div
          key={element.id}
          className="absolute"
          style={{
            left: `${element.x}%`,
            top: `${element.y}%`,
            width: element.size,
            height: element.size,
            opacity: element.opacity,
          }}
        >
          <div
            className="w-full h-full rounded-full"
            style={{
              background: `radial-gradient(circle at center, ${element.color}15, transparent)`,
              filter: 'blur(10px)',
            }}
          />
        </div>
      ))}



      {/* iOS-style ambient light effects */}
      <div
        className="absolute top-0 left-1/4 w-96 h-96 rounded-full opacity-8"
        style={{
          background: 'radial-gradient(circle, rgba(0, 122, 255, 0.1) 0%, transparent 70%)',
          filter: 'blur(40px)',
        }}
      />

      <div
        className="absolute bottom-0 right-1/4 w-80 h-80 rounded-full opacity-10"
        style={{
          background: 'radial-gradient(circle, rgba(90, 200, 250, 0.1) 0%, transparent 70%)',
          filter: 'blur(30px)',
        }}
      />
    </div>
  );
}