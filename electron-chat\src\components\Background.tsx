'use client';

import { motion } from 'framer-motion';
import { useClientMount } from '../hooks/useClientMount';

interface FloatingElement {
  id: number;
  x: number;
  y: number;
  size: number;
  color: string;
  type: 'circle' | 'hexagon' | 'triangle';
  opacity: number;
  duration: number;
  delay: number;
}

export default function Background() {
  const mounted = useClientMount();

  const floatingElements: FloatingElement[] = [
    { id: 1, x: 15, y: 20, size: 120, color: '#667eea', type: 'circle', opacity: 0.15, duration: 8, delay: 0 },
    { id: 2, x: 75, y: 15, size: 80, color: '#764ba2', type: 'hexagon', opacity: 0.12, duration: 12, delay: 2 },
    { id: 3, x: 85, y: 70, size: 100, color: '#f093fb', type: 'circle', opacity: 0.1, duration: 10, delay: 4 },
    { id: 4, x: 25, y: 80, size: 60, color: '#4facfe', type: 'triangle', opacity: 0.18, duration: 15, delay: 1 },
    { id: 5, x: 60, y: 45, size: 140, color: '#00f2fe', type: 'circle', opacity: 0.08, duration: 20, delay: 6 },
    { id: 6, x: 5, y: 60, size: 90, color: '#f5576c', type: 'hexagon', opacity: 0.14, duration: 14, delay: 3 },
  ];

  const gridLines = Array.from({ length: 20 }, (_, i) => i);

  if (!mounted) {
    return (
      <div className="fixed inset-0 -z-10 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900/20 to-slate-900" />
      </div>
    );
  }

  return (
    <div className="fixed inset-0 -z-10 overflow-hidden">
      {/* Animated gradient background */}
      <motion.div
        className="absolute inset-0"
        style={{
          background: 'radial-gradient(ellipse at top, #1a1a2e 0%, #16213e 50%, #0f0f23 100%)',
        }}
        animate={{
          background: [
            'radial-gradient(ellipse at top, #1a1a2e 0%, #16213e 50%, #0f0f23 100%)',
            'radial-gradient(ellipse at bottom right, #1a1a2e 0%, #16213e 50%, #0f0f23 100%)',
            'radial-gradient(ellipse at top left, #1a1a2e 0%, #16213e 50%, #0f0f23 100%)',
            'radial-gradient(ellipse at top, #1a1a2e 0%, #16213e 50%, #0f0f23 100%)',
          ],
        }}
        transition={{
          duration: 30,
          repeat: Infinity,
          ease: 'linear',
        }}
      />

      {/* Grid pattern */}
      <div className="absolute inset-0 opacity-[0.03]">
        <svg width="100%" height="100%" className="absolute inset-0">
          <defs>
            <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
              <path d="M 50 0 L 0 0 0 50" fill="none" stroke="currentColor" strokeWidth="1"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
        </svg>
      </div>

      {/* Floating elements */}
      {floatingElements.map((element) => (
        <motion.div
          key={element.id}
          className="absolute"
          initial={{
            x: `${element.x}%`,
            y: `${element.y}%`,
            opacity: 0,
            scale: 0.5,
          }}
          animate={{
            x: [`${element.x}%`, `${element.x + 10}%`, `${element.x - 5}%`, `${element.x}%`],
            y: [`${element.y}%`, `${element.y - 8}%`, `${element.y + 12}%`, `${element.y}%`],
            opacity: [0, element.opacity, element.opacity, 0],
            scale: [0.5, 1, 0.8, 0.5],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: element.duration,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: element.delay,
          }}
          style={{
            width: element.size,
            height: element.size,
          }}
        >
          {element.type === 'circle' && (
            <div
              className="w-full h-full rounded-full"
              style={{
                background: `radial-gradient(circle at 30% 30%, ${element.color}40, ${element.color}10, transparent)`,
                filter: 'blur(20px)',
              }}
            />
          )}
          {element.type === 'hexagon' && (
            <div
              className="w-full h-full"
              style={{
                clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)',
                background: `linear-gradient(135deg, ${element.color}30, ${element.color}10)`,
                filter: 'blur(15px)',
              }}
            />
          )}
          {element.type === 'triangle' && (
            <div
              className="w-full h-full"
              style={{
                clipPath: 'polygon(50% 0%, 0% 100%, 100% 100%)',
                background: `linear-gradient(45deg, ${element.color}25, ${element.color}05)`,
                filter: 'blur(12px)',
              }}
            />
          )}
        </motion.div>
      ))}

      {/* Particle system */}
      <div className="absolute inset-0">
        {Array.from({ length: 50 }, (_, i) => (
          <motion.div
            key={`particle-${i}`}
            className="absolute w-1 h-1 bg-white rounded-full"
            initial={{
              x: Math.random() * window.innerWidth,
              y: Math.random() * window.innerHeight,
              opacity: 0,
            }}
            animate={{
              y: [null, -100],
              opacity: [0, 0.6, 0],
            }}
            transition={{
              duration: Math.random() * 10 + 10,
              repeat: Infinity,
              delay: Math.random() * 5,
              ease: 'linear',
            }}
          />
        ))}
      </div>

      {/* Ambient light effects */}
      <motion.div
        className="absolute top-0 left-1/4 w-96 h-96 rounded-full"
        style={{
          background: 'radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%)',
          filter: 'blur(60px)',
        }}
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.3, 0.6, 0.3],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
      />

      <motion.div
        className="absolute bottom-0 right-1/4 w-80 h-80 rounded-full"
        style={{
          background: 'radial-gradient(circle, rgba(118, 75, 162, 0.15) 0%, transparent 70%)',
          filter: 'blur(50px)',
        }}
        animate={{
          scale: [1, 0.8, 1],
          opacity: [0.4, 0.7, 0.4],
        }}
        transition={{
          duration: 12,
          repeat: Infinity,
          ease: 'easeInOut',
          delay: 2,
        }}
      />
    </div>
  );
}