'use client';

import { motion } from 'framer-motion';
import { useClientMount } from '../hooks/useClientMount';

interface FloatingElement {
  id: number;
  x: number;
  y: number;
  size: number;
  color: string;
  type: 'circle' | 'hexagon' | 'triangle';
  opacity: number;
  duration: number;
  delay: number;
}

export default function Background() {
  const mounted = useClientMount();

  const floatingElements: FloatingElement[] = [
    { id: 1, x: 15, y: 20, size: 80, color: '#667eea', type: 'circle', opacity: 0.1, duration: 12, delay: 0 },
    { id: 2, x: 75, y: 15, size: 60, color: '#764ba2', type: 'circle', opacity: 0.08, duration: 15, delay: 5 },
    { id: 3, x: 85, y: 70, size: 70, color: '#f093fb', type: 'circle', opacity: 0.06, duration: 18, delay: 10 },
  ];

  const gridLines = Array.from({ length: 20 }, (_, i) => i);

  if (!mounted) {
    return (
      <div className="fixed inset-0 -z-10 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900/20 to-slate-900" />
      </div>
    );
  }

  return (
    <div className="fixed inset-0 -z-10 overflow-hidden">
      {/* Static gradient background */}
      <div
        className="absolute inset-0"
        style={{
          background: 'radial-gradient(ellipse at top, #1a1a2e 0%, #16213e 50%, #0f0f23 100%)',
        }}
      />



      {/* Static floating elements */}
      {floatingElements.map((element) => (
        <div
          key={element.id}
          className="absolute"
          style={{
            left: `${element.x}%`,
            top: `${element.y}%`,
            width: element.size,
            height: element.size,
            opacity: element.opacity,
          }}
        >
          <div
            className="w-full h-full rounded-full"
            style={{
              background: `radial-gradient(circle at center, ${element.color}15, transparent)`,
              filter: 'blur(10px)',
            }}
          />
        </div>
      ))}



      {/* Static ambient light effects */}
      <div
        className="absolute top-0 left-1/4 w-96 h-96 rounded-full opacity-10"
        style={{
          background: 'radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%)',
          filter: 'blur(40px)',
        }}
      />

      <div
        className="absolute bottom-0 right-1/4 w-80 h-80 rounded-full opacity-15"
        style={{
          background: 'radial-gradient(circle, rgba(118, 75, 162, 0.15) 0%, transparent 70%)',
          filter: 'blur(30px)',
        }}
      />
    </div>
  );
}