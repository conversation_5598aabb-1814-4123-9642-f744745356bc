'use client';

import { motion } from 'framer-motion';
import { useClientMount } from '../hooks/useClientMount';

interface Shape {
  x: number;
  y: number;
  size: number;
  color: string;
  type: 'circle' | 'square';
  opacity: number;
}

export default function Background() {
  const mounted = useClientMount();

  const shapes = [
    { x: 10, y: 10, size: 100, color: '#4F46E5', type: 'circle', opacity: 0.3 },
    { x: 80, y: 40, size: 150, color: '#7C3AED', type: 'square', opacity: 0.2 },
    { x: 50, y: 70, size: 120, color: '#2563EB', type: 'circle', opacity: 0.25 },
  ];

  if (!mounted) {
    return <div className="fixed inset-0 -z-10 overflow-hidden" />;
  }

  return (
    <div className="fixed inset-0 -z-10 overflow-hidden">
      {shapes.map((shape, index) => (
        <motion.div
          key={index}
          className={`absolute ${shape.type === 'circle' ? 'rounded-full' : ''}`}
          initial={{
            x: `${shape.x}%`,
            y: `${shape.y}%`,
            opacity: 0,
          }}
          animate={{
            x: `${shape.x}%`,
            y: `${shape.y}%`,
            opacity: shape.opacity,
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            repeatType: 'reverse',
            ease: 'easeInOut',
          }}
          style={{
            width: shape.size,
            height: shape.size,
            background: `radial-gradient(circle at center, ${shape.color}, transparent)`,
            filter: 'blur(40px)',
          }}
        />
      ))}
    </div>
  );
}