import { ScreenshotAnalyzer } from '../analyzers/ScreenshotAnalyzer';
import { ComponentGenerator } from '../generators/ComponentGenerator';
import { readFileSync, readdirSync } from 'fs';
import { join } from 'path';

async function analyzeDevinAIDesign() {
  const analyzer = new ScreenshotAnalyzer();
  const generator = new ComponentGenerator();

  const screenshotsDir = join(process.cwd(), 'screenshots');
  const screenshots = readdirSync(screenshotsDir)
    .filter(file => file.endsWith('.png') || file.endsWith('.jpg'));

  const analysisResults = await Promise.all(
    screenshots.map(async screenshot => {
      const imageBuffer = readFileSync(join(screenshotsDir, screenshot));
      return await analyzer.analyzeScreenshot(imageBuffer);
    })
  );

  const generatedComponents = analysisResults.map(result => 
    generator.generateComponents(result)
  );

  return generatedComponents;
}

export default analyzeDevinAIDesign; 