'use client';

import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';

export default function Hero() {
  const [isClient, setIsClient] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0.5, y: 0.5 });

  useEffect(() => {
    setIsClient(true);
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: e.clientX / window.innerWidth,
        y: e.clientY / window.innerHeight,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      <div className="container mx-auto px-6 py-24 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center max-w-4xl mx-auto"
          style={isClient ? {
            transform: `translate(
              ${(mousePosition.x - 0.5) * 20}px,
              ${(mousePosition.y - 0.5) * 20}px
            )`,
          } : undefined}
        >
          <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-blue-400 to-purple-600 bg-clip-text text-transparent">
            Next-Gen AI Solutions
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 mb-12">
            Harness the power of artificial intelligence to transform your ideas into reality
          </p>
          <motion.div
            className="flex flex-col md:flex-row gap-4 justify-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.8 }}
          >
            <motion.button
              className="px-8 py-4 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium text-lg"
              whileHover={{ scale: 1.05, boxShadow: '0 0 20px rgba(79, 70, 229, 0.4)' }}
              whileTap={{ scale: 0.95 }}
            >
              Get Started
            </motion.button>
            <motion.button
              className="px-8 py-4 rounded-full border-2 border-gray-500 text-white font-medium text-lg hover:border-gray-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Learn More
            </motion.button>
          </motion.div>
        </motion.div>
      </div>

      {/* Decorative elements */}
      <div className="absolute inset-0 z-0">
        {isClient && (
          <>
            <motion.div
              className="absolute w-96 h-96 bg-blue-500/20 rounded-full filter blur-3xl"
              animate={{
                x: mousePosition.x * 100,
                y: mousePosition.y * 100,
                scale: [1, 1.1, 1],
              }}
              transition={{ duration: 0.8 }}
              style={{
                left: '20%',
                top: '20%',
              }}
            />
            <motion.div
              className="absolute w-96 h-96 bg-purple-500/20 rounded-full filter blur-3xl"
              animate={{
                x: -mousePosition.x * 100,
                y: -mousePosition.y * 100,
                scale: [1, 1.2, 1],
              }}
              transition={{ duration: 0.8 }}
              style={{
                right: '20%',
                bottom: '20%',
              }}
            />
          </>
        )}
      </div>
    </section>
  );
}