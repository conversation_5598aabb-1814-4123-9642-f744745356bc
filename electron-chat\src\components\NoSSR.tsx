'use client';

import dynamic from 'next/dynamic';
import { ComponentType } from 'react';

/**
 * Higher-order component that disables SSR for a component
 * This completely prevents hydration mismatches by only rendering on the client
 */
function NoSSR<P extends object>(Component: ComponentType<P>) {
  return dynamic(() => Promise.resolve(Component), {
    ssr: false,
    loading: () => null,
  });
}

export default NoSSR;
