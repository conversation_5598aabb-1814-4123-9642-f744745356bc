(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/src_components_387ae6._.js", {

"[project]/src/components/Background.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__((__turbopack_import__) => {
    return Promise.all([
  "static/chunks/node_modules_5c5ee7._.js",
  "static/chunks/src_067685._.js",
  "static/chunks/src_components_Background_tsx_85c952._.js"
].map((chunk) => __turbopack_load__(chunk))).then(() => {
        return __turbopack_import__("[project]/src/components/Background.tsx [app-client] (ecmascript)");
    });
});
}}),
"[project]/src/components/Header.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__((__turbopack_import__) => {
    return Promise.all([
  "static/chunks/node_modules_162944._.js",
  "static/chunks/src_921215._.js",
  "static/chunks/src_components_Header_tsx_85c952._.js"
].map((chunk) => __turbopack_load__(chunk))).then(() => {
        return __turbopack_import__("[project]/src/components/Header.tsx [app-client] (ecmascript)");
    });
});
}}),
"[project]/src/components/Hero.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__((__turbopack_import__) => {
    return Promise.all([
  "static/chunks/node_modules_f70402._.js",
  "static/chunks/src_fd1c89._.js",
  "static/chunks/src_components_Hero_tsx_85c952._.js"
].map((chunk) => __turbopack_load__(chunk))).then(() => {
        return __turbopack_import__("[project]/src/components/Hero.tsx [app-client] (ecmascript)");
    });
});
}}),
"[project]/src/components/Features.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__((__turbopack_import__) => {
    return Promise.all([
  "static/chunks/node_modules_f70402._.js",
  "static/chunks/src_components_Features_tsx_7c9e7f._.js",
  "static/chunks/src_components_Features_tsx_85c952._.js"
].map((chunk) => __turbopack_load__(chunk))).then(() => {
        return __turbopack_import__("[project]/src/components/Features.tsx [app-client] (ecmascript)");
    });
});
}}),
"[project]/src/components/Testimonials.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__((__turbopack_import__) => {
    return Promise.all([
  "static/chunks/node_modules_f70402._.js",
  "static/chunks/src_components_Testimonials_tsx_5ec8a2._.js",
  "static/chunks/src_components_Testimonials_tsx_85c952._.js"
].map((chunk) => __turbopack_load__(chunk))).then(() => {
        return __turbopack_import__("[project]/src/components/Testimonials.tsx [app-client] (ecmascript)");
    });
});
}}),
"[project]/src/components/Footer.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__((__turbopack_import__) => {
    return Promise.all([
  "static/chunks/node_modules_d7a990._.js",
  "static/chunks/src_components_Footer_tsx_ee364b._.js",
  "static/chunks/src_components_Footer_tsx_85c952._.js"
].map((chunk) => __turbopack_load__(chunk))).then(() => {
        return __turbopack_import__("[project]/src/components/Footer.tsx [app-client] (ecmascript)");
    });
});
}}),
}]);