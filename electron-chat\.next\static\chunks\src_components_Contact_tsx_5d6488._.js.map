{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/src/components/Contact.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useState } from 'react';\n\nexport default function Contact() {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    company: '',\n    message: ''\n  });\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // Handle form submission\n    console.log('Form submitted:', formData);\n  };\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  return (\n    <section id=\"contact\" className=\"py-16 relative overflow-hidden\">\n      <div className=\"container mx-auto px-6\">\n        {/* Section Header */}\n        <motion.div\n          className=\"text-center mb-12\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n        >\n          <motion.div\n            className=\"inline-flex items-center space-x-2 px-4 py-2 rounded-full glass mb-4 text-sm font-medium\"\n            initial={{ opacity: 0, scale: 0.8 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.2 }}\n          >\n            <span className=\"w-2 h-2 bg-green-400 rounded-full\"></span>\n            <span className=\"text-white\">Get In Touch</span>\n          </motion.div>\n\n          <h2 className=\"text-3xl md:text-5xl font-black mb-6 gradient-text-primary\">\n            Contact Us\n          </h2>\n          <p className=\"text-base md:text-lg text-gray-400 max-w-2xl mx-auto leading-relaxed\">\n            Ready to transform your business with AI? Let's discuss how our solutions can help you achieve your goals.\n          </p>\n        </motion.div>\n\n        {/* Contact Form */}\n        <motion.div\n          className=\"max-w-2xl mx-auto\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.2 }}\n          viewport={{ once: true }}\n        >\n          <div className=\"glass-strong rounded-2xl p-8 border border-white/10\">\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-300 mb-2\">\n                    Name\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"name\"\n                    name=\"name\"\n                    value={formData.name}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 rounded-lg bg-white/5 border border-white/10 text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors\"\n                    placeholder=\"Your name\"\n                    required\n                  />\n                </div>\n                <div>\n                  <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-300 mb-2\">\n                    Email\n                  </label>\n                  <input\n                    type=\"email\"\n                    id=\"email\"\n                    name=\"email\"\n                    value={formData.email}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 rounded-lg bg-white/5 border border-white/10 text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors\"\n                    placeholder=\"<EMAIL>\"\n                    required\n                  />\n                </div>\n              </div>\n              \n              <div>\n                <label htmlFor=\"company\" className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Company\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"company\"\n                  name=\"company\"\n                  value={formData.company}\n                  onChange={handleChange}\n                  className=\"w-full px-4 py-3 rounded-lg bg-white/5 border border-white/10 text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors\"\n                  placeholder=\"Your company\"\n                />\n              </div>\n\n              <div>\n                <label htmlFor=\"message\" className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Message\n                </label>\n                <textarea\n                  id=\"message\"\n                  name=\"message\"\n                  value={formData.message}\n                  onChange={handleChange}\n                  rows={5}\n                  className=\"w-full px-4 py-3 rounded-lg bg-white/5 border border-white/10 text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors resize-none\"\n                  placeholder=\"Tell us about your project...\"\n                  required\n                />\n              </div>\n\n              <motion.button\n                type=\"submit\"\n                className=\"w-full px-6 py-3 btn-primary font-semibold text-base flex items-center justify-center space-x-2\"\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n              >\n                <span>Send Message</span>\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\" />\n                </svg>\n              </motion.button>\n            </form>\n          </div>\n        </motion.div>\n\n        {/* Contact Info */}\n        <motion.div\n          className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mt-12\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n        >\n          {[\n            {\n              title: 'Email',\n              value: '<EMAIL>',\n              icon: (\n                <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                </svg>\n              )\n            },\n            {\n              title: 'Phone',\n              value: '+****************',\n              icon: (\n                <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                </svg>\n              )\n            },\n            {\n              title: 'Location',\n              value: 'San Francisco, CA',\n              icon: (\n                <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                </svg>\n              )\n            }\n          ].map((item, index) => (\n            <motion.div\n              key={item.title}\n              className=\"text-center p-6 glass rounded-xl border border-white/10\"\n              whileHover={{ scale: 1.02, y: -5 }}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.6 + index * 0.1 }}\n            >\n              <div className=\"w-12 h-12 rounded-lg bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center mb-4 mx-auto text-white\">\n                {item.icon}\n              </div>\n              <h3 className=\"text-white font-semibold mb-2\">{item.title}</h3>\n              <p className=\"text-gray-400 text-sm\">{item.value}</p>\n            </motion.div>\n          ))}\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AADA;;;AAFA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;IACX;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,yBAAyB;QACzB,QAAQ,GAAG,CAAC,mBAAmB;IACjC;IAEA,MAAM,eAAe,CAAC;QACpB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,aAAa;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BACpC,YAAY;gCAAE,OAAO;4BAAI;;8CAEzB,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;oCAAK,WAAU;8CAAa;;;;;;;;;;;;sCAG/B,6LAAC;4BAAG,WAAU;sCAA6D;;;;;;sCAG3E,6LAAC;4BAAE,WAAU;sCAAuE;;;;;;;;;;;;8BAMtF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAO,WAAU;8DAA+C;;;;;;8DAG/E,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,IAAI;oDACpB,UAAU;oDACV,WAAU;oDACV,aAAY;oDACZ,QAAQ;;;;;;;;;;;;sDAGZ,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAQ,WAAU;8DAA+C;;;;;;8DAGhF,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU;oDACV,WAAU;oDACV,aAAY;oDACZ,QAAQ;;;;;;;;;;;;;;;;;;8CAKd,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAU,WAAU;sDAA+C;;;;;;sDAGlF,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,OAAO;4CACvB,UAAU;4CACV,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAU,WAAU;sDAA+C;;;;;;sDAGlF,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,OAAO;4CACvB,UAAU;4CACV,MAAM;4CACN,WAAU;4CACV,aAAY;4CACZ,QAAQ;;;;;;;;;;;;8CAIZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,MAAK;oCACL,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;;sDAExB,6LAAC;sDAAK;;;;;;sDACN,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ/E,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEtB;wBACC;4BACE,OAAO;4BACP,OAAO;4BACP,oBACE,6LAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;wBAG3E;wBACA;4BACE,OAAO;4BACP,OAAO;4BACP,oBACE,6LAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;wBAG3E;wBACA;4BACE,OAAO;4BACP,OAAO;4BACP,oBACE,6LAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;;kDACjE,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;kDACrE,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;wBAG3E;qBACD,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,YAAY;gCAAE,OAAO;gCAAM,GAAG,CAAC;4BAAE;4BACjC,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,OAAO,MAAM,QAAQ;4BAAI;;8CAEvC,6LAAC;oCAAI,WAAU;8CACZ,KAAK,IAAI;;;;;;8CAEZ,6LAAC;oCAAG,WAAU;8CAAiC,KAAK,KAAK;;;;;;8CACzD,6LAAC;oCAAE,WAAU;8CAAyB,KAAK,KAAK;;;;;;;2BAX3C,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;AAkB7B;GApMwB;KAAA"}}, {"offset": {"line": 505, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}