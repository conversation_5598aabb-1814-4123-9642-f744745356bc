'use client';

import { motion, useScroll, useTransform } from 'framer-motion';
import { useRef } from 'react';

interface FeatureCard {
  title: string;
  description: string;
  icon: string;
  gradient: string;
  stats: { value: string; label: string }[];
  technologies: string[];
}

export default function Features() {
  const ref = useRef(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"]
  });

  const y = useTransform(scrollYProgress, [0, 1], [100, -100]);
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0]);

  const features: FeatureCard[] = [
    {
      title: 'Natural Language Processing',
      description: 'Advanced NLP capabilities for understanding and generating human-like text with state-of-the-art transformer models',
      icon: '🧠',
      gradient: 'from-blue-500/20 to-cyan-500/20',
      stats: [
        { value: '99.8%', label: 'Accuracy' },
        { value: '150+', label: 'Languages' },
      ],
      technologies: ['GPT-4', 'BERT', 'Transformer', 'Neural Networks'],
    },
    {
      title: 'Computer Vision',
      description: 'State-of-the-art image and video analysis with deep learning for real-time object detection and recognition',
      icon: '👁️',
      gradient: 'from-purple-500/20 to-pink-500/20',
      stats: [
        { value: '95%', label: 'Detection Rate' },
        { value: '60fps', label: 'Real-time' },
      ],
      technologies: ['CNN', 'YOLO', 'ResNet', 'OpenCV'],
    },
    {
      title: 'Predictive Analytics',
      description: 'Data-driven insights and forecasting for informed decision making using advanced machine learning algorithms',
      icon: '📊',
      gradient: 'from-green-500/20 to-emerald-500/20',
      stats: [
        { value: '87%', label: 'Prediction Accuracy' },
        { value: '1ms', label: 'Response Time' },
      ],
      technologies: ['Random Forest', 'XGBoost', 'LSTM', 'Time Series'],
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
      },
    },
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 50, rotateX: -15 },
    visible: {
      opacity: 1,
      y: 0,
      rotateX: 0,
      transition: {
        duration: 0.8,
        ease: [0.6, -0.05, 0.01, 0.99],
      },
    },
  };

  return (
    <section id="ai-content" className="py-16 relative overflow-hidden" ref={ref}>
      <motion.div
        className="container mx-auto px-6"
        style={{ y, opacity }}
      >
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <motion.div
            className="inline-flex items-center space-x-2 px-3 py-1.5 rounded-full glass mb-4 text-xs font-medium"
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
          >
            <span className="w-1.5 h-1.5 bg-blue-400 rounded-full animate-pulse"></span>
            <span className="gradient-text-primary">Core Capabilities</span>
          </motion.div>

          <h2 className="text-3xl md:text-5xl font-black mb-6 gradient-text-primary">
            AI Solutions
          </h2>
          <p className="text-base md:text-lg text-gray-300 max-w-2xl mx-auto leading-relaxed">
            Explore our cutting-edge AI solutions powered by the latest advancements in{' '}
            <span className="gradient-text-accent font-semibold">machine learning</span> and{' '}
            <span className="gradient-text-secondary font-semibold">deep neural networks</span>
          </p>
        </motion.div>

        {/* Features Grid */}
        <motion.div
          className="grid grid-cols-1 lg:grid-cols-3 gap-8"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              variants={cardVariants}
              whileHover={{
                scale: 1.02,
                rotateY: 5,
                z: 50,
              }}
              className="group relative"
              style={{
                transformStyle: 'preserve-3d',
                perspective: '1000px',
              }}
            >
              {/* Card Background with Gradient */}
              <div className={`absolute inset-0 bg-gradient-to-br ${feature.gradient} rounded-3xl blur-xl opacity-50 group-hover:opacity-75 transition-opacity duration-500`} />

              {/* Main Card */}
              <div className="relative glass-strong rounded-2xl p-6 h-full border border-white/10 hover:border-white/20 transition-all duration-500">
                {/* Icon */}
                <motion.div
                  className="text-4xl mb-4 flex justify-center"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ duration: 0.3 }}
                >
                  {feature.icon}
                </motion.div>

                {/* Title */}
                <h3 className="text-xl md:text-2xl font-bold mb-3 text-white text-center">
                  {feature.title}
                </h3>

                {/* Description */}
                <p className="text-gray-300 mb-4 leading-relaxed text-center text-sm">
                  {feature.description}
                </p>

                {/* Stats */}
                <div className="flex justify-center space-x-4 mb-4">
                  {feature.stats.map((stat, statIndex) => (
                    <div key={statIndex} className="text-center">
                      <div className="text-lg font-bold gradient-text-primary">
                        {stat.value}
                      </div>
                      <div className="text-xs text-gray-400 uppercase tracking-wider">
                        {stat.label}
                      </div>
                    </div>
                  ))}
                </div>

                {/* Technologies */}
                <div className="mb-4">
                  <div className="text-xs text-gray-400 mb-2 text-center">Technologies:</div>
                  <div className="flex flex-wrap justify-center gap-1">
                    {feature.technologies.map((tech, techIndex) => (
                      <span
                        key={techIndex}
                        className="px-2 py-1 text-xs rounded-full glass text-gray-300 border border-white/10"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>

                {/* CTA Button */}
                <motion.button
                  className="w-full px-4 py-2 rounded-xl btn-primary font-semibold text-sm shadow-lg hover:shadow-xl transition-all duration-300"
                  whileHover={{ scale: 1.02, y: -2 }}
                  whileTap={{ scale: 0.98 }}
                >
                  Explore Technology
                </motion.button>

                {/* Hover Effect Overlay */}
                <motion.div
                  className="absolute inset-0 rounded-3xl bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"
                  initial={{ opacity: 0 }}
                  whileHover={{ opacity: 1 }}
                />
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom CTA Section */}
        <motion.div
          className="text-center mt-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          viewport={{ once: true }}
        >
          <motion.button
            className="px-6 py-3 rounded-xl btn-secondary font-semibold text-base flex items-center space-x-2 mx-auto"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            <span>View All Solutions</span>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
            </svg>
          </motion.button>
        </motion.div>
      </motion.div>

      {/* Background Decorations */}
      <div className="absolute inset-0 -z-10">
        <motion.div
          className="absolute top-1/4 left-10 w-32 h-32 border border-purple-400/20 rounded-full"
          animate={{
            rotate: [0, 360],
            scale: [1, 1.1, 1],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear",
          }}
        />
        <motion.div
          className="absolute bottom-1/4 right-10 w-24 h-24 border border-blue-400/20"
          style={{
            clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)',
          }}
          animate={{
            rotate: [360, 0],
            y: [0, -20, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      </div>
    </section>
  );
}