'use client';

import { motion } from 'framer-motion';

interface FeatureCard {
  title: string;
  description: string;
  icon: string;
}

export default function Features() {
  const features: FeatureCard[] = [
    {
      title: 'Natural Language Processing',
      description: 'Advanced NLP capabilities for understanding and generating human-like text',
      icon: '🤖',
    },
    {
      title: 'Computer Vision',
      description: 'State-of-the-art image and video analysis with deep learning',
      icon: '👁️',
    },
    {
      title: 'Predictive Analytics',
      description: 'Data-driven insights and forecasting for informed decision making',
      icon: '📊',
    },
  ];

  return (
    <section id="ai-content" className="py-24 relative overflow-hidden">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-blue-400 to-purple-600 bg-clip-text text-transparent">
            AI Capabilities
          </h2>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Explore our cutting-edge AI solutions powered by the latest advancements in machine learning
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.2 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.05, rotateY: 5 }}
              className="p-8 rounded-2xl bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 shadow-xl hover:shadow-2xl hover:shadow-blue-500/10 transition-all duration-300"
              style={{
                transformStyle: 'preserve-3d',
                perspective: '1000px',
              }}
            >
              <div className="text-4xl mb-4">{feature.icon}</div>
              <h3 className="text-2xl font-bold mb-4 text-white">{feature.title}</h3>
              <p className="text-gray-300">{feature.description}</p>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="mt-6 px-6 py-2 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium text-sm"
              >
                Learn More
              </motion.button>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}