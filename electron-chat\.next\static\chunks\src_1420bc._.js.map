{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/src/components/Background.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { motion } from 'framer-motion';\n\ninterface Shape {\n  x: number;\n  y: number;\n  size: number;\n  color: string;\n  type: 'circle' | 'square';\n  opacity: number;\n}\n\nexport default function Background() {\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  const shapes = [\n    { x: 10, y: 10, size: 100, color: '#4F46E5', type: 'circle', opacity: 0.3 },\n    { x: 80, y: 40, size: 150, color: '#7C3AED', type: 'square', opacity: 0.2 },\n    { x: 50, y: 70, size: 120, color: '#2563EB', type: 'circle', opacity: 0.25 },\n  ];\n\n  if (!mounted) {\n    return <div className=\"fixed inset-0 -z-10 overflow-hidden\" />;\n  }\n\n  return (\n    <div className=\"fixed inset-0 -z-10 overflow-hidden\">\n      {shapes.map((shape, index) => (\n        <motion.div\n          key={index}\n          className={`absolute ${shape.type === 'circle' ? 'rounded-full' : ''}`}\n          initial={{\n            x: `${shape.x}%`,\n            y: `${shape.y}%`,\n            opacity: 0,\n          }}\n          animate={{\n            x: `${shape.x}%`,\n            y: `${shape.y}%`,\n            opacity: shape.opacity,\n          }}\n          transition={{\n            duration: 2,\n            repeat: Infinity,\n            repeatType: 'reverse',\n            ease: 'easeInOut',\n          }}\n          style={{\n            width: shape.size,\n            height: shape.size,\n            background: `radial-gradient(circle at center, ${shape.color}, transparent)`,\n            filter: 'blur(40px)',\n          }}\n        />\n      ))}\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAce,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,WAAW;QACb;+BAAG,EAAE;IAEL,MAAM,SAAS;QACb;YAAE,GAAG;YAAI,GAAG;YAAI,MAAM;YAAK,OAAO;YAAW,MAAM;YAAU,SAAS;QAAI;QAC1E;YAAE,GAAG;YAAI,GAAG;YAAI,MAAM;YAAK,OAAO;YAAW,MAAM;YAAU,SAAS;QAAI;QAC1E;YAAE,GAAG;YAAI,GAAG;YAAI,MAAM;YAAK,OAAO;YAAW,MAAM;YAAU,SAAS;QAAK;KAC5E;IAED,IAAI,CAAC,SAAS;QACZ,qBAAO,6LAAC;YAAI,WAAU;;;;;;IACxB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,WAAW,CAAC,SAAS,EAAE,MAAM,IAAI,KAAK,WAAW,iBAAiB,IAAI;gBACtE,SAAS;oBACP,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;oBAChB,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;oBAChB,SAAS;gBACX;gBACA,SAAS;oBACP,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;oBAChB,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;oBAChB,SAAS,MAAM,OAAO;gBACxB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,YAAY;oBACZ,MAAM;gBACR;gBACA,OAAO;oBACL,OAAO,MAAM,IAAI;oBACjB,QAAQ,MAAM,IAAI;oBAClB,YAAY,CAAC,kCAAkC,EAAE,MAAM,KAAK,CAAC,cAAc,CAAC;oBAC5E,QAAQ;gBACV;eAvBK;;;;;;;;;;AA4Bf;GAjDwB;KAAA"}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, useScroll } from 'framer-motion';\nimport Link from 'next/link';\n\nexport default function Header() {\n  const [mounted, setMounted] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n  const { scrollY } = useScroll();\n\n  useEffect(() => {\n    setMounted(true);\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navItems = [\n    { name: 'AI Content', href: '#ai-content' },\n    { name: 'Machine Learning', href: '#machine-learning' },\n    { name: 'About Us', href: '#about' },\n    { name: 'Contact', href: '#contact' },\n  ];\n\n  if (!mounted) {\n    return (\n      <header className=\"fixed top-0 left-0 right-0 z-50\">\n        <nav className=\"container mx-auto px-6 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"text-2xl font-bold bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent\">\n              Hydra AI\n            </div>\n            <div className=\"hidden md:flex items-center space-x-8\">\n              {navItems.map((item) => (\n                <div key={item.name}>\n                  <span className=\"text-gray-300\">{item.name}</span>\n                </div>\n              ))}\n            </div>\n            <button className=\"hidden md:block px-6 py-2 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium\">\n              Get Started\n            </button>\n            <button className=\"md:hidden text-white\">\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              </svg>\n            </button>\n          </div>\n        </nav>\n      </header>\n    );\n  }\n\n  return (\n    <motion.header\n      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled ? 'bg-black/50 backdrop-blur-lg' : ''}`}\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ type: 'spring', stiffness: 100 }}\n    >\n      <nav className=\"container mx-auto px-6 py-4\">\n        <div className=\"flex items-center justify-between\">\n          <motion.div\n            className=\"text-2xl font-bold bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent\"\n            whileHover={{ scale: 1.05 }}\n          >\n            Hydra AI\n          </motion.div>\n\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <motion.div\n                key={item.name}\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                <Link\n                  href={item.href}\n                  className=\"text-gray-300 hover:text-white transition-colors duration-200\"\n                  onClick={(e) => {\n                    e.preventDefault();\n                    const element = document.querySelector(item.href);\n                    if (element) {\n                      element.scrollIntoView({ behavior: 'smooth' });\n                    }\n                  }}\n                >\n                  {item.name}\n                </Link>\n              </motion.div>\n            ))}\n          </div>\n\n          <motion.button\n            className=\"hidden md:block px-6 py-2 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            Get Started\n          </motion.button>\n\n          <button className=\"md:hidden text-white\">\n            <svg\n              className=\"w-6 h-6\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d=\"M4 6h16M4 12h16M4 18h16\"\n              />\n            </svg>\n          </button>\n        </div>\n      </nav>\n    </motion.header>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AAEA;AADA;AAAA;;;AAHA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,WAAW;YACX,MAAM;iDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAc,MAAM;QAAc;QAC1C;YAAE,MAAM;YAAoB,MAAM;QAAoB;QACtD;YAAE,MAAM;YAAY,MAAM;QAAS;QACnC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAO,WAAU;sBAChB,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAgG;;;;;;sCAG/G,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC;8CACC,cAAA,6LAAC;wCAAK,WAAU;kDAAiB,KAAK,IAAI;;;;;;mCADlC,KAAK,IAAI;;;;;;;;;;sCAKvB,6LAAC;4BAAO,WAAU;sCAA6G;;;;;;sCAG/H,6LAAC;4BAAO,WAAU;sCAChB,cAAA,6LAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOnF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,WAAW,CAAC,4DAA4D,EAAE,aAAa,iCAAiC,IAAI;QAC5H,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,MAAM;YAAU,WAAW;QAAI;kBAE7C,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;kCAC3B;;;;;;kCAID,6LAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,YAAY;oCAAE,OAAO;gCAAI;gCACzB,UAAU;oCAAE,OAAO;gCAAK;0CAExB,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,CAAC;wCACR,EAAE,cAAc;wCAChB,MAAM,UAAU,SAAS,aAAa,CAAC,KAAK,IAAI;wCAChD,IAAI,SAAS;4CACX,QAAQ,cAAc,CAAC;gDAAE,UAAU;4CAAS;wCAC9C;oCACF;8CAEC,KAAK,IAAI;;;;;;+BAfP,KAAK,IAAI;;;;;;;;;;kCAqBpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;kCACzB;;;;;;kCAID,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC;4BACC,WAAU;4BACV,MAAK;4BACL,QAAO;4BACP,SAAQ;sCAER,cAAA,6LAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,aAAa;gCACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlB;GArHwB;;QAGF,4KAAA,CAAA,YAAS;;;KAHP"}}, {"offset": {"line": 385, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 391, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useEffect, useState } from 'react';\n\nexport default function Hero() {\n  const [mounted, setMounted] = useState(false);\n  const [mousePosition, setMousePosition] = useState({ x: 0.5, y: 0.5 });\n\n  useEffect(() => {\n    setMounted(true);\n    const handleMouseMove = (e: MouseEvent) => {\n      setMousePosition({\n        x: e.clientX / window.innerWidth,\n        y: e.clientY / window.innerHeight,\n      });\n    };\n\n    window.addEventListener('mousemove', handleMouseMove);\n    return () => window.removeEventListener('mousemove', handleMouseMove);\n  }, []);\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      <div className=\"container mx-auto px-6 py-24 relative z-10\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"text-center max-w-4xl mx-auto\"\n          style={mounted ? {\n            transform: `translate(\n              ${(mousePosition.x - 0.5) * 20}px,\n              ${(mousePosition.y - 0.5) * 20}px\n            )`,\n          } : undefined}\n        >\n          <h1 className=\"text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-blue-400 to-purple-600 bg-clip-text text-transparent\">\n            Next-Gen AI Solutions\n          </h1>\n          <p className=\"text-xl md:text-2xl text-gray-300 mb-12\">\n            Harness the power of artificial intelligence to transform your ideas into reality\n          </p>\n          <motion.div\n            className=\"flex flex-col md:flex-row gap-4 justify-center\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3, duration: 0.8 }}\n          >\n            <motion.button\n              className=\"px-8 py-4 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium text-lg\"\n              whileHover={{ scale: 1.05, boxShadow: '0 0 20px rgba(79, 70, 229, 0.4)' }}\n              whileTap={{ scale: 0.95 }}\n            >\n              Get Started\n            </motion.button>\n            <motion.button\n              className=\"px-8 py-4 rounded-full border-2 border-gray-500 text-white font-medium text-lg hover:border-gray-300\"\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              Learn More\n            </motion.button>\n          </motion.div>\n        </motion.div>\n      </div>\n\n      {/* Decorative elements */}\n      <div className=\"absolute inset-0 z-0\">\n        {mounted && (\n          <>\n            <motion.div\n              className=\"absolute w-96 h-96 bg-blue-500/20 rounded-full filter blur-3xl\"\n              animate={{\n                x: mousePosition.x * 100,\n                y: mousePosition.y * 100,\n                scale: [1, 1.1, 1],\n              }}\n              transition={{ duration: 0.8 }}\n              style={{\n                left: '20%',\n                top: '20%',\n              }}\n            />\n            <motion.div\n              className=\"absolute w-96 h-96 bg-purple-500/20 rounded-full filter blur-3xl\"\n              animate={{\n                x: -mousePosition.x * 100,\n                y: -mousePosition.y * 100,\n                scale: [1, 1.2, 1],\n              }}\n              transition={{ duration: 0.8 }}\n              style={{\n                right: '20%',\n                bottom: '20%',\n              }}\n            />\n          </>\n        )}\n      </div>\n    </section>\n  );\n}"], "names": [], "mappings": ";;;;AAGA;AADA;;;AAFA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAK,GAAG;IAAI;IAEpE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,WAAW;YACX,MAAM;kDAAkB,CAAC;oBACvB,iBAAiB;wBACf,GAAG,EAAE,OAAO,GAAG,OAAO,UAAU;wBAChC,GAAG,EAAE,OAAO,GAAG,OAAO,WAAW;oBACnC;gBACF;;YAEA,OAAO,gBAAgB,CAAC,aAAa;YACrC;kCAAO,IAAM,OAAO,mBAAmB,CAAC,aAAa;;QACvD;yBAAG,EAAE;IAEL,qBACE,6LAAC;QAAQ,WAAU;;0BACjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;oBACV,OAAO,UAAU;wBACf,WAAW,CAAC;cACV,EAAE,CAAC,cAAc,CAAC,GAAG,GAAG,IAAI,GAAG;cAC/B,EAAE,CAAC,cAAc,CAAC,GAAG,GAAG,IAAI,GAAG;aAChC,CAAC;oBACJ,IAAI;;sCAEJ,6LAAC;4BAAG,WAAU;sCAAiH;;;;;;sCAG/H,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;sCAGvD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;;8CAExC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,WAAU;oCACV,YAAY;wCAAE,OAAO;wCAAM,WAAW;oCAAkC;oCACxE,UAAU;wCAAE,OAAO;oCAAK;8CACzB;;;;;;8CAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;8CACzB;;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAI,WAAU;0BACZ,yBACC;;sCACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCACP,GAAG,cAAc,CAAC,GAAG;gCACrB,GAAG,cAAc,CAAC,GAAG;gCACrB,OAAO;oCAAC;oCAAG;oCAAK;iCAAE;4BACpB;4BACA,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,OAAO;gCACL,MAAM;gCACN,KAAK;4BACP;;;;;;sCAEF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCACP,GAAG,CAAC,cAAc,CAAC,GAAG;gCACtB,GAAG,CAAC,cAAc,CAAC,GAAG;gCACtB,OAAO;oCAAC;oCAAG;oCAAK;iCAAE;4BACpB;4BACA,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,OAAO;gCACL,OAAO;gCACP,QAAQ;4BACV;;;;;;;;;;;;;;;;;;;AAOd;GAjGwB;KAAA"}}, {"offset": {"line": 599, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 605, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/src/components/Features.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\n\ninterface FeatureCard {\n  title: string;\n  description: string;\n  icon: string;\n}\n\nexport default function Features() {\n  const features: FeatureCard[] = [\n    {\n      title: 'Natural Language Processing',\n      description: 'Advanced NLP capabilities for understanding and generating human-like text',\n      icon: '🤖',\n    },\n    {\n      title: 'Computer Vision',\n      description: 'State-of-the-art image and video analysis with deep learning',\n      icon: '👁️',\n    },\n    {\n      title: 'Predictive Analytics',\n      description: 'Data-driven insights and forecasting for informed decision making',\n      icon: '📊',\n    },\n  ];\n\n  return (\n    <section id=\"ai-content\" className=\"py-24 relative overflow-hidden\">\n      <div className=\"container mx-auto px-6\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-blue-400 to-purple-600 bg-clip-text text-transparent\">\n            AI Capabilities\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-2xl mx-auto\">\n            Explore our cutting-edge AI solutions powered by the latest advancements in machine learning\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n          {features.map((feature, index) => (\n            <motion.div\n              key={index}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: index * 0.2 }}\n              viewport={{ once: true }}\n              whileHover={{ scale: 1.05, rotateY: 5 }}\n              className=\"p-8 rounded-2xl bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 shadow-xl hover:shadow-2xl hover:shadow-blue-500/10 transition-all duration-300\"\n              style={{\n                transformStyle: 'preserve-3d',\n                perspective: '1000px',\n              }}\n            >\n              <div className=\"text-4xl mb-4\">{feature.icon}</div>\n              <h3 className=\"text-2xl font-bold mb-4 text-white\">{feature.title}</h3>\n              <p className=\"text-gray-300\">{feature.description}</p>\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"mt-6 px-6 py-2 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium text-sm\"\n              >\n                Learn More\n              </motion.button>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAUe,SAAS;IACtB,MAAM,WAA0B;QAC9B;YACE,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM;QACR;KACD;IAED,qBACE,6LAAC;QAAQ,IAAG;QAAa,WAAU;kBACjC,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCAAiH;;;;;;sCAG/H,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,OAAO;gCAAM,SAAS;4BAAE;4BACtC,WAAU;4BACV,OAAO;gCACL,gBAAgB;gCAChB,aAAa;4BACf;;8CAEA,6LAAC;oCAAI,WAAU;8CAAiB,QAAQ,IAAI;;;;;;8CAC5C,6LAAC;oCAAG,WAAU;8CAAsC,QAAQ,KAAK;;;;;;8CACjE,6LAAC;oCAAE,WAAU;8CAAiB,QAAQ,WAAW;;;;;;8CACjD,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;8CACX;;;;;;;2BAnBI;;;;;;;;;;;;;;;;;;;;;AA4BnB;KApEwB"}}, {"offset": {"line": 771, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 777, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport Background from '@/components/Background';\nimport Header from '@/components/Header';\nimport Hero from '@/components/Hero';\nimport Features from '@/components/Features';\n\nexport default function Home() {\n  return (\n    <main className=\"min-h-screen bg-black text-white overflow-x-hidden\">\n      <Background />\n      <Header />\n      <Hero />\n      <Features />\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,qBACE,6LAAC;QAAK,WAAU;;0BACd,6LAAC,mIAAA,CAAA,UAAU;;;;;0BACX,6LAAC,+HAAA,CAAA,UAAM;;;;;0BACP,6LAAC,6HAAA,CAAA,UAAI;;;;;0BACL,6LAAC,iIAAA,CAAA,UAAQ;;;;;;;;;;;AAGf;KATwB"}}, {"offset": {"line": 828, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}