{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/src/components/Features.tsx"], "sourcesContent": ["'use client';\n\nimport { motion, useScroll, useTransform } from 'framer-motion';\nimport { useRef } from 'react';\n\ninterface FeatureCard {\n  title: string;\n  description: string;\n  icon: string;\n  gradient: string;\n  stats: { value: string; label: string }[];\n  technologies: string[];\n}\n\nexport default function Features() {\n  const ref = useRef(null);\n  const { scrollYProgress } = useScroll({\n    target: ref,\n    offset: [\"start end\", \"end start\"]\n  });\n\n  const y = useTransform(scrollYProgress, [0, 1], [100, -100]);\n  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0]);\n\n  const features: FeatureCard[] = [\n    {\n      title: 'Natural Language Processing',\n      description: 'Advanced NLP capabilities for understanding and generating human-like text with state-of-the-art transformer models',\n      icon: '🧠',\n      gradient: 'from-blue-500/20 to-cyan-500/20',\n      stats: [\n        { value: '99.8%', label: 'Accuracy' },\n        { value: '150+', label: 'Languages' },\n      ],\n      technologies: ['GPT-4', 'BERT', 'Transformer', 'Neural Networks'],\n    },\n    {\n      title: 'Computer Vision',\n      description: 'State-of-the-art image and video analysis with deep learning for real-time object detection and recognition',\n      icon: '👁️',\n      gradient: 'from-purple-500/20 to-pink-500/20',\n      stats: [\n        { value: '95%', label: 'Detection Rate' },\n        { value: '60fps', label: 'Real-time' },\n      ],\n      technologies: ['CNN', 'YOLO', 'ResNet', 'OpenCV'],\n    },\n    {\n      title: 'Predictive Analytics',\n      description: 'Data-driven insights and forecasting for informed decision making using advanced machine learning algorithms',\n      icon: '📊',\n      gradient: 'from-green-500/20 to-emerald-500/20',\n      stats: [\n        { value: '87%', label: 'Prediction Accuracy' },\n        { value: '1ms', label: 'Response Time' },\n      ],\n      technologies: ['Random Forest', 'XGBoost', 'LSTM', 'Time Series'],\n    },\n  ];\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.3,\n      },\n    },\n  };\n\n  const cardVariants = {\n    hidden: { opacity: 0, y: 50, rotateX: -15 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      rotateX: 0,\n      transition: {\n        duration: 0.8,\n        ease: [0.6, -0.05, 0.01, 0.99],\n      },\n    },\n  };\n\n  return (\n    <section id=\"ai-content\" className=\"py-32 relative overflow-hidden\" ref={ref}>\n      <motion.div\n        className=\"container mx-auto px-6\"\n        style={{ y, opacity }}\n      >\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-20\"\n        >\n          <motion.div\n            className=\"inline-flex items-center space-x-2 px-4 py-2 rounded-full glass mb-6 text-sm font-medium\"\n            initial={{ opacity: 0, scale: 0.8 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.2 }}\n          >\n            <span className=\"w-2 h-2 bg-blue-400 rounded-full animate-pulse\"></span>\n            <span className=\"gradient-text-primary\">Core Capabilities</span>\n          </motion.div>\n\n          <h2 className=\"text-5xl md:text-7xl font-black mb-8 gradient-text-primary\">\n            AI Solutions\n          </h2>\n          <p className=\"text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed\">\n            Explore our cutting-edge AI solutions powered by the latest advancements in{' '}\n            <span className=\"gradient-text-accent font-semibold\">machine learning</span> and{' '}\n            <span className=\"gradient-text-secondary font-semibold\">deep neural networks</span>\n          </p>\n        </motion.div>\n\n        {/* Features Grid */}\n        <motion.div\n          className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\"\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n        >\n          {features.map((feature, index) => (\n            <motion.div\n              key={index}\n              variants={cardVariants}\n              whileHover={{\n                scale: 1.02,\n                rotateY: 5,\n                z: 50,\n              }}\n              className=\"group relative\"\n              style={{\n                transformStyle: 'preserve-3d',\n                perspective: '1000px',\n              }}\n            >\n              {/* Card Background with Gradient */}\n              <div className={`absolute inset-0 bg-gradient-to-br ${feature.gradient} rounded-3xl blur-xl opacity-50 group-hover:opacity-75 transition-opacity duration-500`} />\n\n              {/* Main Card */}\n              <div className=\"relative glass-strong rounded-3xl p-8 h-full border border-white/10 hover:border-white/20 transition-all duration-500\">\n                {/* Icon */}\n                <motion.div\n                  className=\"text-6xl mb-6 flex justify-center\"\n                  whileHover={{ scale: 1.1, rotate: 5 }}\n                  transition={{ duration: 0.3 }}\n                >\n                  {feature.icon}\n                </motion.div>\n\n                {/* Title */}\n                <h3 className=\"text-2xl md:text-3xl font-bold mb-4 text-white text-center\">\n                  {feature.title}\n                </h3>\n\n                {/* Description */}\n                <p className=\"text-gray-300 mb-6 leading-relaxed text-center\">\n                  {feature.description}\n                </p>\n\n                {/* Stats */}\n                <div className=\"flex justify-center space-x-6 mb-6\">\n                  {feature.stats.map((stat, statIndex) => (\n                    <div key={statIndex} className=\"text-center\">\n                      <div className=\"text-2xl font-bold gradient-text-primary\">\n                        {stat.value}\n                      </div>\n                      <div className=\"text-xs text-gray-400 uppercase tracking-wider\">\n                        {stat.label}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n\n                {/* Technologies */}\n                <div className=\"mb-6\">\n                  <div className=\"text-sm text-gray-400 mb-2 text-center\">Technologies:</div>\n                  <div className=\"flex flex-wrap justify-center gap-2\">\n                    {feature.technologies.map((tech, techIndex) => (\n                      <span\n                        key={techIndex}\n                        className=\"px-3 py-1 text-xs rounded-full glass text-gray-300 border border-white/10\"\n                      >\n                        {tech}\n                      </span>\n                    ))}\n                  </div>\n                </div>\n\n                {/* CTA Button */}\n                <motion.button\n                  className=\"w-full px-6 py-3 rounded-2xl btn-primary font-semibold text-sm shadow-lg hover:shadow-xl transition-all duration-300\"\n                  whileHover={{ scale: 1.02, y: -2 }}\n                  whileTap={{ scale: 0.98 }}\n                >\n                  Explore Technology\n                </motion.button>\n\n                {/* Hover Effect Overlay */}\n                <motion.div\n                  className=\"absolute inset-0 rounded-3xl bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none\"\n                  initial={{ opacity: 0 }}\n                  whileHover={{ opacity: 1 }}\n                />\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Bottom CTA Section */}\n        <motion.div\n          className=\"text-center mt-20\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.5 }}\n          viewport={{ once: true }}\n        >\n          <motion.button\n            className=\"px-8 py-4 rounded-2xl btn-secondary font-semibold text-lg flex items-center space-x-2 mx-auto\"\n            whileHover={{ scale: 1.05, y: -2 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <span>View All Solutions</span>\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 7l5 5m0 0l-5 5m5-5H6\" />\n            </svg>\n          </motion.button>\n        </motion.div>\n      </motion.div>\n\n      {/* Background Decorations */}\n      <div className=\"absolute inset-0 -z-10\">\n        <motion.div\n          className=\"absolute top-1/4 left-10 w-32 h-32 border border-purple-400/20 rounded-full\"\n          animate={{\n            rotate: [0, 360],\n            scale: [1, 1.1, 1],\n          }}\n          transition={{\n            duration: 20,\n            repeat: Infinity,\n            ease: \"linear\",\n          }}\n        />\n        <motion.div\n          className=\"absolute bottom-1/4 right-10 w-24 h-24 border border-blue-400/20\"\n          style={{\n            clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)',\n          }}\n          animate={{\n            rotate: [360, 0],\n            y: [0, -20, 0],\n          }}\n          transition={{\n            duration: 15,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n          }}\n        />\n      </div>\n    </section>\n  );\n}"], "names": [], "mappings": ";;;;AAGA;AADA;AAAA;AAAA;;;AAFA;;;AAce,SAAS;;IACtB,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE;QACpC,QAAQ;QACR,QAAQ;YAAC;YAAa;SAAY;IACpC;IAEA,MAAM,IAAI,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;KAAE,EAAE;QAAC;QAAK,CAAC;KAAI;IAC3D,MAAM,UAAU,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;QAAK;QAAK;KAAE,EAAE;QAAC;QAAG;QAAG;QAAG;KAAE;IAE5E,MAAM,WAA0B;QAC9B;YACE,OAAO;YACP,aAAa;YACb,MAAM;YACN,UAAU;YACV,OAAO;gBACL;oBAAE,OAAO;oBAAS,OAAO;gBAAW;gBACpC;oBAAE,OAAO;oBAAQ,OAAO;gBAAY;aACrC;YACD,cAAc;gBAAC;gBAAS;gBAAQ;gBAAe;aAAkB;QACnE;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM;YACN,UAAU;YACV,OAAO;gBACL;oBAAE,OAAO;oBAAO,OAAO;gBAAiB;gBACxC;oBAAE,OAAO;oBAAS,OAAO;gBAAY;aACtC;YACD,cAAc;gBAAC;gBAAO;gBAAQ;gBAAU;aAAS;QACnD;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM;YACN,UAAU;YACV,OAAO;gBACL;oBAAE,OAAO;oBAAO,OAAO;gBAAsB;gBAC7C;oBAAE,OAAO;oBAAO,OAAO;gBAAgB;aACxC;YACD,cAAc;gBAAC;gBAAiB;gBAAW;gBAAQ;aAAc;QACnE;KACD;IAED,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;YAAI,SAAS,CAAC;QAAG;QAC1C,SAAS;YACP,SAAS;YACT,GAAG;YACH,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,MAAM;oBAAC;oBAAK,CAAC;oBAAM;oBAAM;iBAAK;YAChC;QACF;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAa,WAAU;QAAiC,KAAK;;0BACvE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBAAE;oBAAG;gBAAQ;;kCAGpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;gCAAI;;kDAEzB,6LAAC;wCAAK,WAAU;;;;;;kDAChB,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;0CAG1C,6LAAC;gCAAG,WAAU;0CAA6D;;;;;;0CAG3E,6LAAC;gCAAE,WAAU;;oCAAsE;oCACL;kDAC5E,6LAAC;wCAAK,WAAU;kDAAqC;;;;;;oCAAuB;oCAAK;kDACjF,6LAAC;wCAAK,WAAU;kDAAwC;;;;;;;;;;;;;;;;;;kCAK5D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,UAAU;wBACV,SAAQ;wBACR,aAAY;wBACZ,UAAU;4BAAE,MAAM;wBAAK;kCAEtB,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,UAAU;gCACV,YAAY;oCACV,OAAO;oCACP,SAAS;oCACT,GAAG;gCACL;gCACA,WAAU;gCACV,OAAO;oCACL,gBAAgB;oCAChB,aAAa;gCACf;;kDAGA,6LAAC;wCAAI,WAAW,CAAC,mCAAmC,EAAE,QAAQ,QAAQ,CAAC,sFAAsF,CAAC;;;;;;kDAG9J,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,YAAY;oDAAE,OAAO;oDAAK,QAAQ;gDAAE;gDACpC,YAAY;oDAAE,UAAU;gDAAI;0DAE3B,QAAQ,IAAI;;;;;;0DAIf,6LAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAIhB,6LAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;0DAItB,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,0BACxB,6LAAC;wDAAoB,WAAU;;0EAC7B,6LAAC;gEAAI,WAAU;0EACZ,KAAK,KAAK;;;;;;0EAEb,6LAAC;gEAAI,WAAU;0EACZ,KAAK,KAAK;;;;;;;uDALL;;;;;;;;;;0DAYd,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAyC;;;;;;kEACxD,6LAAC;wDAAI,WAAU;kEACZ,QAAQ,YAAY,CAAC,GAAG,CAAC,CAAC,MAAM,0BAC/B,6LAAC;gEAEC,WAAU;0EAET;+DAHI;;;;;;;;;;;;;;;;0DAUb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,WAAU;gDACV,YAAY;oDAAE,OAAO;oDAAM,GAAG,CAAC;gDAAE;gDACjC,UAAU;oDAAE,OAAO;gDAAK;0DACzB;;;;;;0DAKD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,SAAS;gDAAE;gDACtB,YAAY;oDAAE,SAAS;gDAAE;;;;;;;;;;;;;+BA/ExB;;;;;;;;;;kCAuFX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;kCAEvB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,WAAU;4BACV,YAAY;gCAAE,OAAO;gCAAM,GAAG,CAAC;4BAAE;4BACjC,UAAU;gCAAE,OAAO;4BAAK;;8CAExB,6LAAC;8CAAK;;;;;;8CACN,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO7E,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,QAAQ;gCAAC;gCAAG;6BAAI;4BAChB,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;wBACpB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;kCAEF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,OAAO;4BACL,UAAU;wBACZ;wBACA,SAAS;4BACP,QAAQ;gCAAC;gCAAK;6BAAE;4BAChB,GAAG;gCAAC;gCAAG,CAAC;gCAAI;6BAAE;wBAChB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;;;;;;;;;;;;;AAKV;GA5PwB;;QAEM,4KAAA,CAAA,YAAS;QAK3B,+KAAA,CAAA,eAAY;QACN,+KAAA,CAAA,eAAY;;;KARN"}}, {"offset": {"line": 575, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}