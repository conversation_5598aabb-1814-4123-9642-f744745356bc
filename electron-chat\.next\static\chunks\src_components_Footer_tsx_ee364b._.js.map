{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/src/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\n\nexport default function Footer() {\n  const footerSections = [\n    {\n      title: 'Solutions',\n      links: [\n        { name: 'Natural Language Processing', href: '#nlp' },\n        { name: 'Computer Vision', href: '#cv' },\n        { name: 'Predictive Analytics', href: '#analytics' },\n        { name: 'Machine Learning', href: '#ml' },\n      ],\n    },\n    {\n      title: 'Company',\n      links: [\n        { name: 'About Us', href: '#about' },\n        { name: 'Careers', href: '#careers' },\n        { name: 'News', href: '#news' },\n        { name: 'Contact', href: '#contact' },\n      ],\n    },\n    {\n      title: 'Resources',\n      links: [\n        { name: 'Documentation', href: '#docs' },\n        { name: 'API Reference', href: '#api' },\n        { name: 'Tutorials', href: '#tutorials' },\n        { name: 'Support', href: '#support' },\n      ],\n    },\n  ];\n\n  const socialLinks = [\n    { name: 'Twitter', icon: '🐦', href: '#twitter' },\n    { name: 'LinkedIn', icon: '💼', href: '#linkedin' },\n    { name: 'GitHub', icon: '🐙', href: '#github' },\n    { name: 'Discord', icon: '💬', href: '#discord' },\n  ];\n\n  return (\n    <footer className=\"relative bg-black/50 backdrop-blur-lg border-t border-white/10\">\n      <div className=\"container mx-auto px-6 py-12\">\n        {/* Main Footer Content */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-2\">\n            <motion.div\n              className=\"flex items-center space-x-3 mb-4\"\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n            >\n              <div className=\"w-10 h-10 rounded-xl glass flex items-center justify-center\">\n                <span className=\"text-xl\">🧠</span>\n              </div>\n              <div className=\"text-2xl font-bold gradient-text-primary\">\n                Hydra AI\n              </div>\n            </motion.div>\n\n            <motion.p\n              className=\"text-gray-300 mb-4 leading-relaxed max-w-md text-sm\"\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.1 }}\n              viewport={{ once: true }}\n            >\n              Empowering the future with cutting-edge artificial intelligence solutions.\n              Transform your business with our advanced AI technologies.\n            </motion.p>\n\n            {/* Newsletter Signup */}\n            <motion.div\n              className=\"space-y-2\"\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n              viewport={{ once: true }}\n            >\n              <h4 className=\"text-white font-semibold text-sm\">Stay Updated</h4>\n              <div className=\"flex space-x-2\">\n                <input\n                  type=\"email\"\n                  placeholder=\"Enter your email\"\n                  className=\"flex-1 px-3 py-2 rounded-lg glass border border-white/10 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors text-sm\"\n                />\n                <motion.button\n                  className=\"px-4 py-2 rounded-lg btn-primary font-semibold text-sm\"\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  Subscribe\n                </motion.button>\n              </div>\n            </motion.div>\n          </div>\n\n          {/* Footer Links */}\n          {footerSections.map((section, index) => (\n            <motion.div\n              key={section.title}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.1 * (index + 3) }}\n              viewport={{ once: true }}\n            >\n              <h4 className=\"text-white font-semibold mb-3 text-sm\">{section.title}</h4>\n              <ul className=\"space-y-1.5\">\n                {section.links.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-gray-400 hover:text-white transition-colors duration-200 text-xs\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Bottom Section */}\n        <motion.div\n          className=\"pt-6 border-t border-white/10 flex flex-col md:flex-row justify-between items-center space-y-3 md:space-y-0\"\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.5 }}\n          viewport={{ once: true }}\n        >\n          {/* Copyright */}\n          <div className=\"text-gray-400 text-xs\">\n            © 2024 Hydra AI. All rights reserved.\n          </div>\n\n          {/* Social Links */}\n          <div className=\"flex space-x-3\">\n            {socialLinks.map((social) => (\n              <motion.a\n                key={social.name}\n                href={social.href}\n                className=\"w-8 h-8 rounded-lg glass flex items-center justify-center text-gray-400 hover:text-white transition-colors duration-200\"\n                whileHover={{ scale: 1.1, y: -2 }}\n                whileTap={{ scale: 0.95 }}\n                title={social.name}\n              >\n                <span className=\"text-sm\">{social.icon}</span>\n              </motion.a>\n            ))}\n          </div>\n\n          {/* Legal Links */}\n          <div className=\"flex space-x-4 text-xs\">\n            <Link href=\"#privacy\" className=\"text-gray-400 hover:text-white transition-colors\">\n              Privacy Policy\n            </Link>\n            <Link href=\"#terms\" className=\"text-gray-400 hover:text-white transition-colors\">\n              Terms of Service\n            </Link>\n          </div>\n        </motion.div>\n      </div>\n\n      {/* Background Decoration */}\n      <div className=\"absolute inset-0 -z-10\">\n        <motion.div\n          className=\"absolute bottom-0 left-1/4 w-64 h-64 rounded-full\"\n          style={{\n            background: 'radial-gradient(circle, rgba(102, 126, 234, 0.05) 0%, transparent 70%)',\n            filter: 'blur(40px)',\n          }}\n          animate={{\n            scale: [1, 1.1, 1],\n            opacity: [0.3, 0.5, 0.3],\n          }}\n          transition={{\n            duration: 8,\n            repeat: Infinity,\n            ease: 'easeInOut',\n          }}\n        />\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AADA;AAFA;;;;AAKe,SAAS;IACtB,MAAM,iBAAiB;QACrB;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAA+B,MAAM;gBAAO;gBACpD;oBAAE,MAAM;oBAAmB,MAAM;gBAAM;gBACvC;oBAAE,MAAM;oBAAwB,MAAM;gBAAa;gBACnD;oBAAE,MAAM;oBAAoB,MAAM;gBAAM;aACzC;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAAS;gBACnC;oBAAE,MAAM;oBAAW,MAAM;gBAAW;gBACpC;oBAAE,MAAM;oBAAQ,MAAM;gBAAQ;gBAC9B;oBAAE,MAAM;oBAAW,MAAM;gBAAW;aACrC;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAiB,MAAM;gBAAQ;gBACvC;oBAAE,MAAM;oBAAiB,MAAM;gBAAO;gBACtC;oBAAE,MAAM;oBAAa,MAAM;gBAAa;gBACxC;oBAAE,MAAM;oBAAW,MAAM;gBAAW;aACrC;QACH;KACD;IAED,MAAM,cAAc;QAClB;YAAE,MAAM;YAAW,MAAM;YAAM,MAAM;QAAW;QAChD;YAAE,MAAM;YAAY,MAAM;YAAM,MAAM;QAAY;QAClD;YAAE,MAAM;YAAU,MAAM;YAAM,MAAM;QAAU;QAC9C;YAAE,MAAM;YAAW,MAAM;YAAM,MAAM;QAAW;KACjD;IAED,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,UAAU;4CAAE,MAAM;wCAAK;;0DAEvB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;0DAE5B,6LAAC;gDAAI,WAAU;0DAA2C;;;;;;;;;;;;kDAK5D,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wCACP,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;kDACxB;;;;;;kDAMD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;;0DAEvB,6LAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,WAAU;;;;;;kEAEZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wDACZ,WAAU;wDACV,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;kEACzB;;;;;;;;;;;;;;;;;;;;;;;;4BAQN,eAAe,GAAG,CAAC,CAAC,SAAS,sBAC5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,MAAM,CAAC,QAAQ,CAAC;oCAAE;oCACtD,UAAU;wCAAE,MAAM;oCAAK;;sDAEvB,6LAAC;4CAAG,WAAU;sDAAyC,QAAQ,KAAK;;;;;;sDACpE,6LAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL,KAAK,IAAI;;;;;;;;;;;mCATjB,QAAQ,KAAK;;;;;;;;;;;kCAwBxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;;0CAGvB,6LAAC;gCAAI,WAAU;0CAAwB;;;;;;0CAKvC,6LAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wCAEP,MAAM,OAAO,IAAI;wCACjB,WAAU;wCACV,YAAY;4CAAE,OAAO;4CAAK,GAAG,CAAC;wCAAE;wCAChC,UAAU;4CAAE,OAAO;wCAAK;wCACxB,OAAO,OAAO,IAAI;kDAElB,cAAA,6LAAC;4CAAK,WAAU;sDAAW,OAAO,IAAI;;;;;;uCAPjC,OAAO,IAAI;;;;;;;;;;0CAatB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAmD;;;;;;kDAGnF,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAmD;;;;;;;;;;;;;;;;;;;;;;;;0BAQvF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,OAAO;wBACL,YAAY;wBACZ,QAAQ;oBACV;oBACA,SAAS;wBACP,OAAO;4BAAC;4BAAG;4BAAK;yBAAE;wBAClB,SAAS;4BAAC;4BAAK;4BAAK;yBAAI;oBAC1B;oBACA,YAAY;wBACV,UAAU;wBACV,QAAQ;wBACR,MAAM;oBACR;;;;;;;;;;;;;;;;;AAKV;KAzLwB"}}, {"offset": {"line": 459, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}