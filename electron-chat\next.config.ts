import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  reactStrictMode: false, // Disable strict mode to prevent hydration issues in development
  experimental: {
    optimizePackageImports: ['framer-motion'],
  },
  // Additional configuration to handle hydration issues
  compiler: {
    // Remove console.logs in production
    removeConsole: process.env.NODE_ENV === 'production',
  },
  // Disable server-side rendering for development to prevent hydration mismatches
  ...(process.env.NODE_ENV === 'development' && {
    experimental: {
      ...nextConfig.experimental,
      ssr: false,
    },
  }),
};

export default nextConfig;
