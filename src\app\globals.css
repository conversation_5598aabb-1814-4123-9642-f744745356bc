@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 17, 24, 39;
  --background-end-rgb: 31, 41, 55;
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-start-rgb));
  min-height: 100vh;
  overflow-x: hidden;
}

.hexagon-background {
  position: fixed;
  inset: 0;
  z-index: -1;
  overflow: hidden;
  background: linear-gradient(
    to bottom,
    rgb(var(--background-start-rgb)),
    rgb(var(--background-end-rgb))
  );
}

.hexagon-grid {
  position: absolute;
  inset: 0;
  opacity: 0.3;
  transition: opacity 0.3s ease;
}

.hexagon {
  position: absolute;
  transition: opacity 0.3s ease;
  backdrop-filter: blur(8px);
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}