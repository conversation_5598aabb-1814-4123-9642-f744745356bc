{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/src/components/Testimonials.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\n\ninterface Testimonial {\n  name: string;\n  role: string;\n  company: string;\n  content: string;\n  avatar: string;\n  rating: number;\n}\n\nexport default function Testimonials() {\n\n  const testimonials: Testimonial[] = [\n    {\n      name: \"<PERSON>\",\n      role: \"CT<PERSON>\",\n      company: \"TechFlow Inc.\",\n      content: \"Hydra AI transformed our data processing capabilities. The accuracy and speed improvements are remarkable. Our team productivity increased by 300%.\",\n      avatar: \"👩‍💼\",\n      rating: 5,\n    },\n    {\n      name: \"<PERSON>\",\n      role: \"Lead Developer\",\n      company: \"InnovateLab\",\n      content: \"The computer vision solutions provided by Hydra AI are cutting-edge. Implementation was seamless and the results exceeded our expectations.\",\n      avatar: \"👨‍💻\",\n      rating: 5,\n    },\n    {\n      name: \"<PERSON>\",\n      role: \"Data Scientist\",\n      company: \"Analytics Pro\",\n      content: \"Outstanding predictive analytics platform. The machine learning models are incredibly accurate and the insights have driven our business growth.\",\n      avatar: \"👩‍🔬\",\n      rating: 5,\n    },\n  ];\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2,\n      },\n    },\n  };\n\n  const cardVariants = {\n    hidden: { opacity: 0, y: 50, scale: 0.9 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      scale: 1,\n      transition: {\n        duration: 0.8,\n        ease: [0.6, -0.05, 0.01, 0.99],\n      },\n    },\n  };\n\n  return (\n    <section id=\"testimonials\" className=\"py-16 relative overflow-hidden\">\n      <div className=\"container mx-auto px-6\">\n        {/* Section Header */}\n        <motion.div\n          className=\"text-center mb-12\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n        >\n          <motion.div\n            className=\"inline-flex items-center space-x-2 px-3 py-1.5 rounded-full glass mb-4 text-xs font-medium\"\n            initial={{ opacity: 0, scale: 0.8 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.2 }}\n          >\n            <span className=\"w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse\"></span>\n            <span className=\"gradient-text-secondary\">Client Success Stories</span>\n          </motion.div>\n\n          <h2 className=\"text-3xl md:text-5xl font-black mb-6 gradient-text-primary\">\n            Testimonials\n          </h2>\n          <p className=\"text-base md:text-lg text-gray-300 max-w-2xl mx-auto leading-relaxed\">\n            Discover how leading companies are{' '}\n            <span className=\"gradient-text-accent font-semibold\">transforming their business</span>{' '}\n            with our AI solutions\n          </p>\n        </motion.div>\n\n        {/* Testimonials Grid */}\n        <motion.div\n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\"\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n        >\n          {testimonials.map((testimonial, index) => (\n            <motion.div\n              key={index}\n              variants={cardVariants}\n              whileHover={{\n                scale: 1.02,\n                y: -5,\n              }}\n              className=\"group relative\"\n            >\n              {/* Card Background Glow */}\n              <div className=\"absolute inset-0 bg-gradient-to-br from-yellow-500/10 to-orange-500/10 rounded-3xl blur-xl opacity-50 group-hover:opacity-75 transition-opacity duration-500\" />\n\n              {/* Main Card */}\n              <div className=\"relative glass-strong rounded-2xl p-6 h-full border border-white/10 hover:border-white/20 transition-all duration-500\">\n                {/* Rating Stars */}\n                <div className=\"flex justify-center mb-4\">\n                  {Array.from({ length: testimonial.rating }, (_, i) => (\n                    <motion.span\n                      key={i}\n                      className=\"text-yellow-400 text-base\"\n                      initial={{ opacity: 0, scale: 0 }}\n                      whileInView={{ opacity: 1, scale: 1 }}\n                      transition={{ delay: 0.1 * i }}\n                    >\n                      ⭐\n                    </motion.span>\n                  ))}\n                </div>\n\n                {/* Quote */}\n                <blockquote className=\"text-gray-300 text-center mb-6 leading-relaxed italic text-sm\">\n                  \"{testimonial.content}\"\n                </blockquote>\n\n                {/* Author Info */}\n                <div className=\"text-center\">\n                  <motion.div\n                    className=\"w-12 h-12 rounded-full glass flex items-center justify-center text-2xl mx-auto mb-3\"\n                    whileHover={{ scale: 1.1, rotate: 5 }}\n                  >\n                    {testimonial.avatar}\n                  </motion.div>\n\n                  <h4 className=\"text-white font-bold text-base mb-1\">\n                    {testimonial.name}\n                  </h4>\n                  <p className=\"text-gray-400 text-xs mb-1\">\n                    {testimonial.role}\n                  </p>\n                  <p className=\"gradient-text-accent text-xs font-semibold\">\n                    {testimonial.company}\n                  </p>\n                </div>\n\n                {/* Hover Effect Overlay */}\n                <motion.div\n                  className=\"absolute inset-0 rounded-3xl bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none\"\n                  initial={{ opacity: 0 }}\n                  whileHover={{ opacity: 1 }}\n                />\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Stats Section */}\n        <motion.div\n          className=\"mt-12 grid grid-cols-2 md:grid-cols-4 gap-6\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.5 }}\n          viewport={{ once: true }}\n        >\n          {[\n            { number: '500+', label: 'Happy Clients' },\n            { number: '99.9%', label: 'Uptime' },\n            { number: '24/7', label: 'Support' },\n            { number: '50+', label: 'Countries' },\n          ].map((stat, index) => (\n            <motion.div\n              key={stat.label}\n              className=\"text-center\"\n              whileHover={{ scale: 1.05 }}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.8 + index * 0.1 }}\n            >\n              <div className=\"text-2xl md:text-3xl font-bold gradient-text-primary mb-1\">\n                {stat.number}\n              </div>\n              <div className=\"text-gray-400 text-xs uppercase tracking-wider\">\n                {stat.label}\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n      </div>\n\n      {/* Background Decorations */}\n      <div className=\"absolute inset-0 -z-10\">\n        <motion.div\n          className=\"absolute top-1/3 right-10 w-40 h-40 border border-yellow-400/20 rounded-full\"\n          animate={{\n            rotate: [0, 360],\n            scale: [1, 1.2, 1],\n          }}\n          transition={{\n            duration: 25,\n            repeat: Infinity,\n            ease: \"linear\",\n          }}\n        />\n        <motion.div\n          className=\"absolute bottom-1/3 left-10 w-32 h-32 border border-orange-400/20\"\n          style={{\n            clipPath: 'polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)',\n          }}\n          animate={{\n            rotate: [360, 0],\n            y: [0, -30, 0],\n          }}\n          transition={{\n            duration: 18,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n          }}\n        />\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAae,SAAS;IAEtB,MAAM,eAA8B;QAClC;YACE,MAAM;YACN,MAAM;YACN,SAAS;YACT,SAAS;YACT,QAAQ;YACR,QAAQ;QACV;QACA;YACE,MAAM;YACN,MAAM;YACN,SAAS;YACT,SAAS;YACT,QAAQ;YACR,QAAQ;QACV;QACA;YACE,MAAM;YACN,MAAM;YACN,SAAS;YACT,SAAS;YACT,QAAQ;YACR,QAAQ;QACV;KACD;IAED,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;YAAI,OAAO;QAAI;QACxC,SAAS;YACP,SAAS;YACT,GAAG;YACH,OAAO;YACP,YAAY;gBACV,UAAU;gBACV,MAAM;oBAAC;oBAAK,CAAC;oBAAM;oBAAM;iBAAK;YAChC;QACF;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAe,WAAU;;0BACnC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;;0CAEvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;gCAAI;;kDAEzB,6LAAC;wCAAK,WAAU;;;;;;kDAChB,6LAAC;wCAAK,WAAU;kDAA0B;;;;;;;;;;;;0CAG5C,6LAAC;gCAAG,WAAU;0CAA6D;;;;;;0CAG3E,6LAAC;gCAAE,WAAU;;oCAAuE;oCAC/C;kDACnC,6LAAC;wCAAK,WAAU;kDAAqC;;;;;;oCAAmC;oCAAI;;;;;;;;;;;;;kCAMhG,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,UAAU;wBACV,SAAQ;wBACR,aAAY;wBACZ,UAAU;4BAAE,MAAM;wBAAK;kCAEtB,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,UAAU;gCACV,YAAY;oCACV,OAAO;oCACP,GAAG,CAAC;gCACN;gCACA,WAAU;;kDAGV,6LAAC;wCAAI,WAAU;;;;;;kDAGf,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;0DACZ,MAAM,IAAI,CAAC;oDAAE,QAAQ,YAAY,MAAM;gDAAC,GAAG,CAAC,GAAG,kBAC9C,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;wDAEV,WAAU;wDACV,SAAS;4DAAE,SAAS;4DAAG,OAAO;wDAAE;wDAChC,aAAa;4DAAE,SAAS;4DAAG,OAAO;wDAAE;wDACpC,YAAY;4DAAE,OAAO,MAAM;wDAAE;kEAC9B;uDALM;;;;;;;;;;0DAYX,6LAAC;gDAAW,WAAU;;oDAAgE;oDAClF,YAAY,OAAO;oDAAC;;;;;;;0DAIxB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,YAAY;4DAAE,OAAO;4DAAK,QAAQ;wDAAE;kEAEnC,YAAY,MAAM;;;;;;kEAGrB,6LAAC;wDAAG,WAAU;kEACX,YAAY,IAAI;;;;;;kEAEnB,6LAAC;wDAAE,WAAU;kEACV,YAAY,IAAI;;;;;;kEAEnB,6LAAC;wDAAE,WAAU;kEACV,YAAY,OAAO;;;;;;;;;;;;0DAKxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,SAAS;gDAAE;gDACtB,YAAY;oDAAE,SAAS;gDAAE;;;;;;;;;;;;;+BAzDxB;;;;;;;;;;kCAiEX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;kCAEtB;4BACC;gCAAE,QAAQ;gCAAQ,OAAO;4BAAgB;4BACzC;gCAAE,QAAQ;gCAAS,OAAO;4BAAS;4BACnC;gCAAE,QAAQ;gCAAQ,OAAO;4BAAU;4BACnC;gCAAE,QAAQ;gCAAO,OAAO;4BAAY;yBACrC,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAU;gCACV,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO,MAAM,QAAQ;gCAAI;;kDAEvC,6LAAC;wCAAI,WAAU;kDACZ,KAAK,MAAM;;;;;;kDAEd,6LAAC;wCAAI,WAAU;kDACZ,KAAK,KAAK;;;;;;;+BAXR,KAAK,KAAK;;;;;;;;;;;;;;;;0BAmBvB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,QAAQ;gCAAC;gCAAG;6BAAI;4BAChB,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;wBACpB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;kCAEF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,OAAO;4BACL,UAAU;wBACZ;wBACA,SAAS;4BACP,QAAQ;gCAAC;gCAAK;6BAAE;4BAChB,GAAG;gCAAC;gCAAG,CAAC;gCAAI;6BAAE;wBAChB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;;;;;;;;;;;;;AAKV;KA9NwB"}}, {"offset": {"line": 464, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}