@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #000000;
  --foreground: #ffffff;
  --ios-blue: #007AFF;
  --ios-blue-light: #5AC8FA;
  --ios-gray: #8E8E93;
  --ios-gray-light: #C7C7CC;
  --ios-gray-dark: #48484A;
  --ios-background: #1C1C1E;
  --ios-secondary: #2C2C2E;
  --ios-tertiary: #3A3A3C;
  --primary-gradient: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  --secondary-gradient: linear-gradient(135deg, #34C759 0%, #30D158 100%);
  --accent-gradient: linear-gradient(135deg, #FF9500 0%, #FFCC02 100%);
  --glass-bg: rgba(255, 255, 255, 0.08);
  --glass-border: rgba(255, 255, 255, 0.12);
  --shadow-color: rgba(0, 0, 0, 0.4);
}

* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  scroll-padding-top: 80px; /* Account for fixed header */
}

/* Enhanced smooth scrolling */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}

body {
  color: var(--foreground);
  background: linear-gradient(180deg, #000000 0%, var(--ios-background) 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, sans-serif;
  overflow-x: hidden;
  min-height: 100vh;
  /* Performance optimizations */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #764ba2, #667eea);
}

/* iOS-style glass morphism */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(20px) saturate(180%);
  border: 0.5px solid var(--glass-border);
}

.glass-strong {
  background: rgba(28, 28, 30, 0.8);
  backdrop-filter: blur(20px) saturate(180%);
  border: 0.5px solid rgba(255, 255, 255, 0.15);
}

/* Gradient text utilities */
.gradient-text-primary {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-secondary {
  background: var(--secondary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-accent {
  background: var(--accent-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* iOS-style button styles */
.btn-primary {
  background: var(--ios-blue);
  border: none;
  color: white;
  font-weight: 600;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  border-radius: 12px;
}

.btn-primary:hover {
  background: var(--ios-blue-light);
  transform: scale(1.02);
}

.btn-primary:active {
  transform: scale(0.98);
}

.btn-secondary {
  background: var(--ios-secondary);
  border: 0.5px solid var(--ios-gray-dark);
  color: var(--ios-blue);
  font-weight: 600;
  transition: all 0.2s ease;
  position: relative;
  border-radius: 12px;
}

.btn-secondary:hover {
  background: var(--ios-tertiary);
  transform: scale(1.02);
}

.btn-secondary:active {
  transform: scale(0.98);
}

/* Animation utilities */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes glow {
  from { box-shadow: 0 0 20px var(--glow-blue); }
  to { box-shadow: 0 0 30px var(--glow-purple), 0 0 40px var(--glow-blue); }
}

/* Optimize fixed elements */
.fixed {
  transform: translateZ(0);
}

/* Hide browser extension attributes to prevent hydration mismatches */
[bis_skin_checked],
[webcrx],
[__processed_0561e652-f089-4c1b-88da-52458fca1d20__] {
  /* These attributes are added by browser extensions and can cause hydration issues */
  /* We don't remove them but ensure they don't affect styling */
}
