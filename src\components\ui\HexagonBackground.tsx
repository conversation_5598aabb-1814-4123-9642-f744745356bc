"use client";

import { motion, useMotionValue, useTransform } from 'framer-motion';
import React, { useEffect, useState } from 'react';

'use client';

import { useEffect, useState } from 'react';

export const HexagonBackground = () => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);
  const [mouseSpeed, setMouseSpeed] = useState(0);
  const [lastMousePos, setLastMousePos] = useState({ x: 0, y: 0 });
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });
  const glowIntensity = useMotionValue(0);
  const glowOpacity = useTransform(glowIntensity, [0, 100], [0.2, 0.6]);

  useEffect(() => {
    let lastTime = performance.now();

    const handleMouseMove = (e: MouseEvent) => {
      const currentTime = performance.now();
      const timeDiff = currentTime - lastTime;
      const currentPos = { x: e.clientX, y: e.clientY };
      
      // Calculate speed
      const distance = Math.sqrt(
        Math.pow(currentPos.x - lastMousePos.x, 2) +
        Math.pow(currentPos.y - lastMousePos.y, 2)
      );
      const speed = distance / timeDiff;
      
      setMouseSpeed(Math.min(speed * 10, 100));
      setLastMousePos(currentPos);
      setMousePos(currentPos);
      glowIntensity.set(speed * 10);
      
      lastTime = currentTime;
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, [lastMousePos]);

  const hexagons = Array.from({ length: 30 }).map((_, i) => {
    const row = Math.floor(i / 5);
    const col = i % 5;
    const x = col * 200 + (row % 2) * 100;
    const y = row * 175;

    return { x, y, id: i };
  });

  return (
    <div className="fixed inset-0 -z-10 overflow-hidden bg-gray-900">
      <div className="absolute inset-0 opacity-30">
        {isClient && hexagons.map((hexagon) => {
          const distance = Math.sqrt(
            Math.pow(mousePos.x - (hexagon.x + 100), 2) +
            Math.pow(mousePos.y - (hexagon.y + 87.5), 2)
          );
          const maxDistance = 300;
          const intensity = Math.max(0, 1 - distance / maxDistance);

          return (
            <motion.div
              key={hexagon.id}
              className="absolute w-[200px] h-[175px]"
              style={{
                left: hexagon.x,
                top: hexagon.y,
                opacity: useTransform(
                  glowIntensity,
                  [0, 100],
                  [0.2, intensity * useTransform(glowOpacity, [0.2, 0.6], [0.4, 1]).get()]
                ),
              }}
            >
              <div
                className="w-full h-full"
                style={{
                  clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)',
                  background: `radial-gradient(circle at center, rgba(59, 130, 246, 0.5), rgba(99, 102, 241, 0.2))`,
                  backdropFilter: 'blur(8px)',
                }}
              />
            </motion.div>
          );
        })}
      </div>
    </div>
  );
};