'use client';

import BackgroundNoSSR from '@/components/BackgroundNoSSR';
import Header from '@/components/Header';
import HeroNoSSR from '@/components/HeroNoSSR';
import Features from '@/components/Features';
import ClientOnly from '@/components/ClientOnly';

export default function Home() {
  return (
    <main className="min-h-screen bg-black text-white overflow-x-hidden">
      <BackgroundNoSSR />
      <ClientOnly
        fallback={
          <header className="fixed top-0 left-0 right-0 z-50">
            <nav className="container mx-auto px-6 py-4">
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent">
                  Hydra AI
                </div>
                <div className="hidden md:flex items-center space-x-8">
                  <span className="text-gray-300">AI Content</span>
                  <span className="text-gray-300">Machine Learning</span>
                  <span className="text-gray-300">About Us</span>
                  <span className="text-gray-300">Contact</span>
                </div>
                <button className="hidden md:block px-6 py-2 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium">
                  Get Started
                </button>
              </div>
            </nav>
          </header>
        }
      >
        <Header />
      </ClientOnly>
      <HeroNoSSR />
      <Features />
    </main>
  );
}
