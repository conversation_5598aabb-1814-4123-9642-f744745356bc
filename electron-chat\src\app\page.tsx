'use client';

import dynamic from 'next/dynamic';

// Dynamically import all components with no SSR to prevent hydration issues
const Background = dynamic(() => import('@/components/Background'), {
  ssr: false,
  loading: () => null,
});

const Header = dynamic(() => import('@/components/Header'), {
  ssr: false,
  loading: () => (
    <header className="fixed top-0 left-0 right-0 z-50">
      <nav className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="text-2xl font-bold bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent">
            Hydra AI
          </div>
          <div className="hidden md:flex items-center space-x-8">
            <span className="text-gray-300">AI Content</span>
            <span className="text-gray-300">Machine Learning</span>
            <span className="text-gray-300">About Us</span>
            <span className="text-gray-300">Contact</span>
          </div>
          <button className="hidden md:block px-6 py-2 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium">
            Get Started
          </button>
        </div>
      </nav>
    </header>
  ),
});

const Hero = dynamic(() => import('@/components/Hero'), {
  ssr: false,
  loading: () => (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      <div className="container mx-auto px-6 py-24 relative z-10">
        <div className="text-center max-w-4xl mx-auto">
          <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-blue-400 to-purple-600 bg-clip-text text-transparent">
            Next-Gen AI Solutions
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 mb-12">
            Harness the power of artificial intelligence to transform your ideas into reality
          </p>
          <div className="flex flex-col md:flex-row gap-4 justify-center">
            <button className="px-8 py-4 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium text-lg">
              Get Started
            </button>
            <button className="px-8 py-4 rounded-full border-2 border-gray-500 text-white font-medium text-lg hover:border-gray-300">
              Learn More
            </button>
          </div>
        </div>
      </div>
    </section>
  ),
});

const Features = dynamic(() => import('@/components/Features'), {
  ssr: false,
  loading: () => (
    <section className="py-24 relative overflow-hidden">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-blue-400 to-purple-600 bg-clip-text text-transparent">
            AI Capabilities
          </h2>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Explore our cutting-edge AI solutions powered by the latest advancements in machine learning
          </p>
        </div>
      </div>
    </section>
  ),
});

const Testimonials = dynamic(() => import('@/components/Testimonials'), {
  ssr: false,
  loading: () => (
    <section className="py-24 relative overflow-hidden">
      <div className="container mx-auto px-6 text-center">
        <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-blue-400 to-purple-600 bg-clip-text text-transparent">
          Testimonials
        </h2>
        <p className="text-xl text-gray-300 max-w-2xl mx-auto">
          What our clients say about our AI solutions
        </p>
      </div>
    </section>
  ),
});

const Contact = dynamic(() => import('@/components/Contact'), {
  ssr: false,
  loading: () => (
    <section className="py-24 relative overflow-hidden">
      <div className="container mx-auto px-6 text-center">
        <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-blue-400 to-purple-600 bg-clip-text text-transparent">
          Contact Us
        </h2>
        <p className="text-xl text-gray-300 max-w-2xl mx-auto">
          Get in touch to discuss your AI project
        </p>
      </div>
    </section>
  ),
});

const Footer = dynamic(() => import('@/components/Footer'), {
  ssr: false,
  loading: () => (
    <footer className="bg-black/50 backdrop-blur-lg border-t border-white/10 py-16">
      <div className="container mx-auto px-6 text-center">
        <div className="text-2xl font-bold gradient-text-primary mb-4">Hydra AI</div>
        <p className="text-gray-400">© 2024 Hydra AI. All rights reserved.</p>
      </div>
    </footer>
  ),
});

export default function Home() {
  return (
    <main className="min-h-screen bg-black text-white">
      <Background />
      <Header />
      <Hero />
      <Features />
      <Testimonials />
      <Contact />
      <Footer />
    </main>
  );
}
