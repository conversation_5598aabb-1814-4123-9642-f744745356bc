"use client";
import { motion } from 'framer-motion';
import React, { useEffect, useState } from 'react';

export const AnimatedBackground = () => {
  const [isClient, setIsClient] = useState(false);
  const [particles, setParticles] = useState(() => {
    // Initialize with empty array to avoid hydration mismatch
    return [];
  });

  useEffect(() => {
    // Generate particles only on client-side
    const generateParticles = () => {
      return Array.from({ length: 20 }).map(() => ({
        x: Math.random() * 100,
        y: Math.random() * 100,
        scale: Math.random() * 2 + 1,
        duration: Math.random() * 10 + 20,
        size: Math.random() * 300 + 100,
      }));
    };
    
    setIsClient(true);
    setParticles(generateParticles());
  }, []);

  // Render the same base structure for both server and client
  return (
    <div className="absolute inset-0 overflow-hidden -z-10">
      <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 opacity-80" />
      {isClient && (
        <motion.div
          className="absolute inset-0"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1 }}
        >
          {particles.map((particle, i) => (
            <motion.div
              key={i}
              className="absolute rounded-full bg-blue-500/10"
              initial={{
                x: `${particle.x}%`,
                y: `${particle.y}%`,
                scale: 0
              }}
              animate={{
                x: `${particle.x}%`,
                y: `${particle.y}%`,
                scale: particle.scale
              }}
              transition={{
                duration: particle.duration,
                repeat: Infinity,
                repeatType: "reverse"
              }}
              style={{
                width: particle.size,
                height: particle.size,
              }}
            />
          ))}
        </motion.div>
      )}
    </div>
  );
};