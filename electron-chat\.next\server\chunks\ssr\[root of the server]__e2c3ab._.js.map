{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/src/utils/browserExtensionHandler.ts"], "sourcesContent": ["/**\n * Utility functions to handle browser extension interference\n */\n\nexport function isBrowserExtensionPresent(): boolean {\n  if (typeof window === 'undefined') return false;\n  \n  // Check for common browser extension attributes\n  const extensionAttributes = [\n    'bis_skin_checked',\n    'webcrx',\n    '__processed_0561e652-f089-4c1b-88da-52458fca1d20__',\n    'data-extension-id',\n  ];\n  \n  return extensionAttributes.some(attr => \n    document.querySelector(`[${attr}]`) !== null\n  );\n}\n\nexport function suppressHydrationWarnings(): void {\n  if (typeof window === 'undefined') return;\n  \n  // Temporarily suppress hydration warnings in development\n  if (process.env.NODE_ENV === 'development') {\n    const originalError = console.error;\n    console.error = (...args: any[]) => {\n      if (\n        typeof args[0] === 'string' &&\n        args[0].includes('hydration') &&\n        args[0].includes('mismatch')\n      ) {\n        // Suppress hydration mismatch errors caused by browser extensions\n        return;\n      }\n      originalError.apply(console, args);\n    };\n  }\n}\n\nexport function cleanupExtensionAttributes(): void {\n  if (typeof window === 'undefined') return;\n  \n  // Remove browser extension attributes after component mount\n  setTimeout(() => {\n    const extensionAttributes = [\n      'bis_skin_checked',\n      'webcrx',\n      '__processed_0561e652-f089-4c1b-88da-52458fca1d20__',\n    ];\n    \n    extensionAttributes.forEach(attr => {\n      const elements = document.querySelectorAll(`[${attr}]`);\n      elements.forEach(el => {\n        el.removeAttribute(attr);\n      });\n    });\n  }, 100);\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;AAEM,SAAS;IACd,wCAAmC,OAAO;;IAE1C,gDAAgD;IAChD,MAAM;AAUR;AAEO,SAAS;IACd,wCAAmC;;AAiBrC;AAEO,SAAS;IACd,wCAAmC;;AAiBrC"}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/src/components/AppWrapper.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { suppressHydrationWarnings, cleanupExtensionAttributes } from '@/utils/browserExtensionHandler';\n\ninterface AppWrapperProps {\n  children: React.ReactNode;\n}\n\nexport default function AppWrapper({ children }: AppWrapperProps) {\n  useEffect(() => {\n    // Handle browser extension interference\n    suppressHydrationWarnings();\n    cleanupExtensionAttributes();\n    \n    // Set up a mutation observer to clean up extension attributes as they're added\n    const observer = new MutationObserver((mutations) => {\n      mutations.forEach((mutation) => {\n        if (mutation.type === 'attributes') {\n          const target = mutation.target as Element;\n          const extensionAttributes = [\n            'bis_skin_checked',\n            'webcrx',\n            '__processed_0561e652-f089-4c1b-88da-52458fca1d20__',\n          ];\n          \n          extensionAttributes.forEach(attr => {\n            if (target.hasAttribute(attr)) {\n              target.removeAttribute(attr);\n            }\n          });\n        }\n      });\n    });\n    \n    // Start observing\n    observer.observe(document.body, {\n      attributes: true,\n      subtree: true,\n      attributeFilter: [\n        'bis_skin_checked',\n        'webcrx',\n        '__processed_0561e652-f089-4c1b-88da-52458fca1d20__',\n      ],\n    });\n    \n    return () => {\n      observer.disconnect();\n    };\n  }, []);\n\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASe,SAAS,WAAW,EAAE,QAAQ,EAAmB;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAwC;QACxC,CAAA,GAAA,uIAAA,CAAA,4BAAyB,AAAD;QACxB,CAAA,GAAA,uIAAA,CAAA,6BAA0B,AAAD;QAEzB,+EAA+E;QAC/E,MAAM,WAAW,IAAI,iBAAiB,CAAC;YACrC,UAAU,OAAO,CAAC,CAAC;gBACjB,IAAI,SAAS,IAAI,KAAK,cAAc;oBAClC,MAAM,SAAS,SAAS,MAAM;oBAC9B,MAAM,sBAAsB;wBAC1B;wBACA;wBACA;qBACD;oBAED,oBAAoB,OAAO,CAAC,CAAA;wBAC1B,IAAI,OAAO,YAAY,CAAC,OAAO;4BAC7B,OAAO,eAAe,CAAC;wBACzB;oBACF;gBACF;YACF;QACF;QAEA,kBAAkB;QAClB,SAAS,OAAO,CAAC,SAAS,IAAI,EAAE;YAC9B,YAAY;YACZ,SAAS;YACT,iBAAiB;gBACf;gBACA;gBACA;aACD;QACH;QAEA,OAAO;YACL,SAAS,UAAU;QACrB;IACF,GAAG,EAAE;IAEL,qBAAO;kBAAG;;AACZ"}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n    } else if (process.env.TURBOPACK) {\n      module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n    } else {\n      module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n    } else if (process.env.TURBOPACK) {\n      module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n    } else {\n      module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAQzC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1CJ,OAAOC,OAAO,GAAGC,QAAQ;QAC3B,OAAO,IAAIL,QAAQC,GAAG,CAACO,SAAS,EAAE;;QAIlC;IACF;AACF", "ignoreList": [0]}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,0HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0]}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,0HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0]}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}