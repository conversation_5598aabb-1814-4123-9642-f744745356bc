"use client";
import { motion } from "framer-motion";

const features = [
  {
    title: "Autonomous Development",
    description: "Handles complex software engineering tasks independently, from bug fixes to feature implementation.",
    icon: "🤖",
  },
  {
    title: "Code Analysis",
    description: "Advanced code understanding and real-time analysis for optimal solutions.",
    icon: "📊",
  },
  {
    title: "Workflow Integration",
    description: "Seamlessly integrates with your existing development workflow and tools.",
    icon: "⚡",
  },
  {
    title: "Multi-Language Support",
    description: "Works across multiple programming languages and frameworks efficiently.",
    icon: "🌐",
  },
  {
    title: "Continuous Learning",
    description: "Constantly improves and adapts to new programming patterns and best practices.",
    icon: "🧠",
  },
  {
    title: "Team Collaboration",
    description: "Works alongside human developers, enhancing team productivity.",
    icon: "👥",
  },
];

export const Features = () => {
  return (
    <section className="py-24 bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Powerful Features for Modern Development
          </h2>
          <p className="text-xl text-gray-400">
            Experience the future of software development with Devin
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              className="bg-gray-800 p-8 rounded-xl border border-gray-700 hover:border-blue-500 transition-all"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ scale: 1.02 }}
            >
              <div className="text-4xl mb-4">{feature.icon}</div>
              <h3 className="text-xl font-semibold mb-2 text-white">{feature.title}</h3>
              <p className="text-gray-400">{feature.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}; 