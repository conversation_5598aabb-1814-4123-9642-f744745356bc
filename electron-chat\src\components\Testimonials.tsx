'use client';

import { motion, useScroll, useTransform } from 'framer-motion';
import { useRef } from 'react';

interface Testimonial {
  name: string;
  role: string;
  company: string;
  content: string;
  avatar: string;
  rating: number;
}

export default function Testimonials() {
  const ref = useRef(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"]
  });

  const y = useTransform(scrollYProgress, [0, 1], [50, -50]);

  const testimonials: Testimonial[] = [
    {
      name: "<PERSON>",
      role: "CTO",
      company: "TechFlow Inc.",
      content: "Hydra AI transformed our data processing capabilities. The accuracy and speed improvements are remarkable. Our team productivity increased by 300%.",
      avatar: "👩‍💼",
      rating: 5,
    },
    {
      name: "<PERSON>",
      role: "Lead Developer",
      company: "InnovateLab",
      content: "The computer vision solutions provided by Hydra AI are cutting-edge. Implementation was seamless and the results exceeded our expectations.",
      avatar: "👨‍💻",
      rating: 5,
    },
    {
      name: "<PERSON>",
      role: "Data Scientist",
      company: "Analytics Pro",
      content: "Outstanding predictive analytics platform. The machine learning models are incredibly accurate and the insights have driven our business growth.",
      avatar: "👩‍🔬",
      rating: 5,
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.9 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.8,
        ease: [0.6, -0.05, 0.01, 0.99],
      },
    },
  };

  return (
    <section id="testimonials" className="py-32 relative overflow-hidden" ref={ref}>
      <motion.div 
        className="container mx-auto px-6"
        style={{ y }}
      >
        {/* Section Header */}
        <motion.div
          className="text-center mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.div
            className="inline-flex items-center space-x-2 px-4 py-2 rounded-full glass mb-6 text-sm font-medium"
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
          >
            <span className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></span>
            <span className="gradient-text-secondary">Client Success Stories</span>
          </motion.div>

          <h2 className="text-5xl md:text-7xl font-black mb-8 gradient-text-primary">
            Testimonials
          </h2>
          <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Discover how leading companies are{' '}
            <span className="gradient-text-accent font-semibold">transforming their business</span>{' '}
            with our AI solutions
          </p>
        </motion.div>

        {/* Testimonials Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              variants={cardVariants}
              whileHover={{ 
                scale: 1.05,
                rotateY: 5,
                z: 50,
              }}
              className="group relative"
              style={{
                transformStyle: 'preserve-3d',
                perspective: '1000px',
              }}
            >
              {/* Card Background Glow */}
              <div className="absolute inset-0 bg-gradient-to-br from-yellow-500/10 to-orange-500/10 rounded-3xl blur-xl opacity-50 group-hover:opacity-75 transition-opacity duration-500" />
              
              {/* Main Card */}
              <div className="relative glass-strong rounded-3xl p-8 h-full border border-white/10 hover:border-white/20 transition-all duration-500">
                {/* Rating Stars */}
                <div className="flex justify-center mb-6">
                  {Array.from({ length: testimonial.rating }, (_, i) => (
                    <motion.span
                      key={i}
                      className="text-yellow-400 text-xl"
                      initial={{ opacity: 0, scale: 0 }}
                      whileInView={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.1 * i }}
                    >
                      ⭐
                    </motion.span>
                  ))}
                </div>

                {/* Quote */}
                <blockquote className="text-gray-300 text-center mb-8 leading-relaxed italic">
                  "{testimonial.content}"
                </blockquote>

                {/* Author Info */}
                <div className="text-center">
                  <motion.div
                    className="w-16 h-16 rounded-full glass flex items-center justify-center text-3xl mx-auto mb-4"
                    whileHover={{ scale: 1.1, rotate: 5 }}
                  >
                    {testimonial.avatar}
                  </motion.div>
                  
                  <h4 className="text-white font-bold text-lg mb-1">
                    {testimonial.name}
                  </h4>
                  <p className="text-gray-400 text-sm mb-1">
                    {testimonial.role}
                  </p>
                  <p className="gradient-text-accent text-sm font-semibold">
                    {testimonial.company}
                  </p>
                </div>

                {/* Hover Effect Overlay */}
                <motion.div
                  className="absolute inset-0 rounded-3xl bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"
                  initial={{ opacity: 0 }}
                  whileHover={{ opacity: 1 }}
                />
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Stats Section */}
        <motion.div
          className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          viewport={{ once: true }}
        >
          {[
            { number: '500+', label: 'Happy Clients' },
            { number: '99.9%', label: 'Uptime' },
            { number: '24/7', label: 'Support' },
            { number: '50+', label: 'Countries' },
          ].map((stat, index) => (
            <motion.div
              key={stat.label}
              className="text-center"
              whileHover={{ scale: 1.05 }}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 + index * 0.1 }}
            >
              <div className="text-4xl md:text-5xl font-bold gradient-text-primary mb-2">
                {stat.number}
              </div>
              <div className="text-gray-400 text-sm uppercase tracking-wider">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </motion.div>
      </motion.div>

      {/* Background Decorations */}
      <div className="absolute inset-0 -z-10">
        <motion.div
          className="absolute top-1/3 right-10 w-40 h-40 border border-yellow-400/20 rounded-full"
          animate={{
            rotate: [0, 360],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear",
          }}
        />
        <motion.div
          className="absolute bottom-1/3 left-10 w-32 h-32 border border-orange-400/20"
          style={{
            clipPath: 'polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)',
          }}
          animate={{
            rotate: [360, 0],
            y: [0, -30, 0],
          }}
          transition={{
            duration: 18,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      </div>
    </section>
  );
}
