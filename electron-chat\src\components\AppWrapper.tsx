'use client';

import { useEffect } from 'react';
import { suppressHydrationWarnings, cleanupExtensionAttributes } from '@/utils/browserExtensionHandler';

interface AppWrapperProps {
  children: React.ReactNode;
}

export default function AppWrapper({ children }: AppWrapperProps) {
  useEffect(() => {
    // Handle browser extension interference
    suppressHydrationWarnings();
    cleanupExtensionAttributes();
    
    // Set up a mutation observer to clean up extension attributes as they're added
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes') {
          const target = mutation.target as Element;
          const extensionAttributes = [
            'bis_skin_checked',
            'webcrx',
            '__processed_0561e652-f089-4c1b-88da-52458fca1d20__',
          ];
          
          extensionAttributes.forEach(attr => {
            if (target.hasAttribute(attr)) {
              target.removeAttribute(attr);
            }
          });
        }
      });
    });
    
    // Start observing
    observer.observe(document.body, {
      attributes: true,
      subtree: true,
      attributeFilter: [
        'bis_skin_checked',
        'webcrx',
        '__processed_0561e652-f089-4c1b-88da-52458fca1d20__',
      ],
    });
    
    return () => {
      observer.disconnect();
    };
  }, []);

  return <>{children}</>;
}
