"use client";
import { motion } from "framer-motion";

const testimonials = [
  {
    quote: "<PERSON> has transformed our development process, increasing efficiency by 300%",
    author: "<PERSON>",
    role: "CTO at TechCorp",
    image: "/avatars/avatar1.jpg"
  },
  {
    quote: "The autonomous capabilities of <PERSON> have revolutionized how we handle complex engineering tasks",
    author: "<PERSON>",
    role: "Lead Developer at InnovateTech",
    image: "/avatars/avatar2.jpg"
  },
  {
    quote: "<PERSON>'s ability to understand and improve existing codebases is remarkable",
    author: "<PERSON>",
    role: "Engineering Manager at CodeFlow",
    image: "/avatars/avatar3.jpg"
  }
];

export const Testimonials = () => {
  return (
    <section className="py-24 bg-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Trusted by Leading Companies
          </h2>
          <p className="text-xl text-gray-400">
            See how Devin is transforming software development
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              className="bg-gray-900 p-8 rounded-xl"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1 }}
            >
              <blockquote className="text-xl text-gray-300 mb-6">
                "{testimonial.quote}"
              </blockquote>
              <div className="flex items-center">
                <div className="h-12 w-12 rounded-full bg-gray-700 mr-4" />
                <div>
                  <div className="font-semibold text-white">{testimonial.author}</div>
                  <div className="text-gray-400">{testimonial.role}</div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}; 