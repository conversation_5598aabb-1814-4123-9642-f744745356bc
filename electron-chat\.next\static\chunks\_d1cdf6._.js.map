{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport dynamic from 'next/dynamic';\n\n// Dynamically import all components with no SSR to prevent hydration issues\nconst Background = dynamic(() => import('@/components/Background'), {\n  ssr: false,\n  loading: () => null,\n});\n\nconst Header = dynamic(() => import('@/components/Header'), {\n  ssr: false,\n  loading: () => (\n    <header className=\"fixed top-0 left-0 right-0 z-50\">\n      <nav className=\"container mx-auto px-6 py-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"text-2xl font-bold bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent\">\n            Hydra AI\n          </div>\n          <div className=\"hidden md:flex items-center space-x-8\">\n            <span className=\"text-gray-300\">AI Content</span>\n            <span className=\"text-gray-300\">Machine Learning</span>\n            <span className=\"text-gray-300\">About Us</span>\n            <span className=\"text-gray-300\">Contact</span>\n          </div>\n          <button className=\"hidden md:block px-6 py-2 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium\">\n            Get Started\n          </button>\n        </div>\n      </nav>\n    </header>\n  ),\n});\n\nconst Hero = dynamic(() => import('@/components/Hero'), {\n  ssr: false,\n  loading: () => (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      <div className=\"container mx-auto px-6 py-24 relative z-10\">\n        <div className=\"text-center max-w-4xl mx-auto\">\n          <h1 className=\"text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-blue-400 to-purple-600 bg-clip-text text-transparent\">\n            Next-Gen AI Solutions\n          </h1>\n          <p className=\"text-xl md:text-2xl text-gray-300 mb-12\">\n            Harness the power of artificial intelligence to transform your ideas into reality\n          </p>\n          <div className=\"flex flex-col md:flex-row gap-4 justify-center\">\n            <button className=\"px-8 py-4 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium text-lg\">\n              Get Started\n            </button>\n            <button className=\"px-8 py-4 rounded-full border-2 border-gray-500 text-white font-medium text-lg hover:border-gray-300\">\n              Learn More\n            </button>\n          </div>\n        </div>\n      </div>\n    </section>\n  ),\n});\n\nconst Features = dynamic(() => import('@/components/Features'), {\n  ssr: false,\n  loading: () => (\n    <section className=\"py-24 relative overflow-hidden\">\n      <div className=\"container mx-auto px-6\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-blue-400 to-purple-600 bg-clip-text text-transparent\">\n            AI Capabilities\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-2xl mx-auto\">\n            Explore our cutting-edge AI solutions powered by the latest advancements in machine learning\n          </p>\n        </div>\n      </div>\n    </section>\n  ),\n});\n\nconst Testimonials = dynamic(() => import('@/components/Testimonials'), {\n  ssr: false,\n  loading: () => (\n    <section className=\"py-24 relative overflow-hidden\">\n      <div className=\"container mx-auto px-6 text-center\">\n        <h2 className=\"text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-blue-400 to-purple-600 bg-clip-text text-transparent\">\n          Testimonials\n        </h2>\n        <p className=\"text-xl text-gray-300 max-w-2xl mx-auto\">\n          What our clients say about our AI solutions\n        </p>\n      </div>\n    </section>\n  ),\n});\n\nconst Contact = dynamic(() => import('@/components/Contact'), {\n  ssr: false,\n  loading: () => (\n    <section className=\"py-24 relative overflow-hidden\">\n      <div className=\"container mx-auto px-6 text-center\">\n        <h2 className=\"text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-blue-400 to-purple-600 bg-clip-text text-transparent\">\n          Contact Us\n        </h2>\n        <p className=\"text-xl text-gray-300 max-w-2xl mx-auto\">\n          Get in touch to discuss your AI project\n        </p>\n      </div>\n    </section>\n  ),\n});\n\nconst Footer = dynamic(() => import('@/components/Footer'), {\n  ssr: false,\n  loading: () => (\n    <footer className=\"bg-black/50 backdrop-blur-lg border-t border-white/10 py-16\">\n      <div className=\"container mx-auto px-6 text-center\">\n        <div className=\"text-2xl font-bold gradient-text-primary mb-4\">Hydra AI</div>\n        <p className=\"text-gray-400\">© 2024 Hydra AI. All rights reserved.</p>\n      </div>\n    </footer>\n  ),\n});\n\nexport default function Home() {\n  return (\n    <main className=\"min-h-screen bg-black text-white overflow-x-hidden\">\n      <Background />\n      <Header />\n      <Hero />\n      <Features />\n      <Testimonials />\n      <Contact />\n      <Footer />\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,4EAA4E;AAC5E,MAAM,aAAa,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IACzB,KAAK;IACL,SAAS,IAAM;;KAFX;AAKN,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IACrB,KAAK;IACL,SAAS,kBACP,6LAAC;YAAO,WAAU;sBAChB,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAgG;;;;;;sCAG/G,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAElC,6LAAC;4BAAO,WAAU;sCAA6G;;;;;;;;;;;;;;;;;;;;;;;MAfnI;AAwBN,MAAM,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IACnB,KAAK;IACL,SAAS,kBACP,6LAAC;YAAQ,WAAU;sBACjB,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAiH;;;;;;sCAG/H,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;sCAGvD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAO,WAAU;8CAAqG;;;;;;8CAGvH,6LAAC;oCAAO,WAAU;8CAAuG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAhB/H;AA0BN,MAAM,WAAW,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IACvB,KAAK;IACL,SAAS,kBACP,6LAAC;YAAQ,WAAU;sBACjB,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAiH;;;;;;sCAG/H,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;;;;;;;;;;;;MAT3D;AAkBN,MAAM,eAAe,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IAC3B,KAAK;IACL,SAAS,kBACP,6LAAC;YAAQ,WAAU;sBACjB,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAiH;;;;;;kCAG/H,6LAAC;wBAAE,WAAU;kCAA0C;;;;;;;;;;;;;;;;;;MARzD;AAgBN,MAAM,UAAU,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IACtB,KAAK;IACL,SAAS,kBACP,6LAAC;YAAQ,WAAU;sBACjB,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAiH;;;;;;kCAG/H,6LAAC;wBAAE,WAAU;kCAA0C;;;;;;;;;;;;;;;;;;MARzD;AAgBN,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IACrB,KAAK;IACL,SAAS,kBACP,6LAAC;YAAO,WAAU;sBAChB,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAgD;;;;;;kCAC/D,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;MAN/B;AAYS,SAAS;IACtB,qBACE,6LAAC;QAAK,WAAU;;0BACd,6LAAC;;;;;0BACD,6LAAC;;;;;0BACD,6LAAC;;;;;0BACD,6LAAC;;;;;0BACD,6LAAC;;;;;0BACD,6LAAC;;;;;0BACD,6LAAC;;;;;;;;;;;AAGP;MAZwB"}}, {"offset": {"line": 418, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 428, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/node_modules/next/src/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.tsx"], "sourcesContent": ["'use client'\n\nimport type { ReactElement } from 'react'\nimport { BailoutToCSRError } from './bailout-to-csr'\n\ninterface BailoutToCSRProps {\n  reason: string\n  children: ReactElement\n}\n\n/**\n * If rendered on the server, this component throws an error\n * to signal Next.js that it should bail out to client-side rendering instead.\n */\nexport function BailoutToCSR({ reason, children }: BailoutToCSRProps) {\n  if (typeof window === 'undefined') {\n    throw new BailoutToCSRError(reason)\n  }\n\n  return children\n}\n"], "names": ["BailoutToCSR", "reason", "children", "window", "BailoutToCSRError"], "mappings": "AAAA;;;;;+BAcgBA,gBAAAA;;;eAAAA;;;8BAXkB;AAW3B,SAASA,aAAa,KAAuC;IAAvC,IAAA,EAAEC,MAAM,EAAEC,QAAQ,EAAqB,GAAvC;IAC3B,IAAI,OAAOC,WAAW,aAAa;QACjC,MAAM,IAAIC,cAAAA,iBAAiB,CAACH;IAC9B;IAEA,OAAOC;AACT", "ignoreList": [0]}}, {"offset": {"line": 447, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/node_modules/next/src/shared/lib/encode-uri-path.ts"], "sourcesContent": ["export function encodeURIPath(file: string) {\n  return file\n    .split('/')\n    .map((p) => encodeURIComponent(p))\n    .join('/')\n}\n"], "names": ["encodeURIPath", "file", "split", "map", "p", "encodeURIComponent", "join"], "mappings": ";;;;+BAAgBA,iBAAAA;;;eAAAA;;;AAAT,SAASA,cAAcC,IAAY;IACxC,OAAOA,KACJC,KAAK,CAAC,KACNC,GAAG,CAAC,CAACC,IAAMC,mBAAmBD,IAC9BE,IAAI,CAAC;AACV", "ignoreList": [0]}}, {"offset": {"line": 465, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 470, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/node_modules/next/src/shared/lib/lazy-dynamic/preload-chunks.tsx"], "sourcesContent": ["'use client'\n\nimport { preload } from 'react-dom'\n\nimport { workAsyncStorage } from '../../../server/app-render/work-async-storage.external'\nimport { encodeURIPath } from '../encode-uri-path'\n\nexport function PreloadChunks({\n  moduleIds,\n}: {\n  moduleIds: string[] | undefined\n}) {\n  // Early return in client compilation and only load requestStore on server side\n  if (typeof window !== 'undefined') {\n    return null\n  }\n\n  const workStore = workAsyncStorage.getStore()\n  if (workStore === undefined) {\n    return null\n  }\n\n  const allFiles = []\n\n  // Search the current dynamic call unique key id in react loadable manifest,\n  // and find the corresponding CSS files to preload\n  if (workStore.reactLoadableManifest && moduleIds) {\n    const manifest = workStore.reactLoadableManifest\n    for (const key of moduleIds) {\n      if (!manifest[key]) continue\n      const chunks = manifest[key].files\n      allFiles.push(...chunks)\n    }\n  }\n\n  if (allFiles.length === 0) {\n    return null\n  }\n\n  return (\n    <>\n      {allFiles.map((chunk) => {\n        const href = `${workStore.assetPrefix}/_next/${encodeURIPath(chunk)}`\n        const isCss = chunk.endsWith('.css')\n        // If it's stylesheet we use `precedence` o help hoist with React Float.\n        // For stylesheets we actually need to render the CSS because nothing else is going to do it so it needs to be part of the component tree.\n        // The `preload` for stylesheet is not optional.\n        if (isCss) {\n          return (\n            <link\n              key={chunk}\n              // @ts-ignore\n              precedence=\"dynamic\"\n              href={href}\n              rel=\"stylesheet\"\n              as=\"style\"\n            />\n          )\n        } else {\n          // If it's script we use ReactDOM.preload to preload the resources\n          preload(href, {\n            as: 'script',\n            fetchPriority: 'low',\n          })\n          return null\n        }\n      })}\n    </>\n  )\n}\n"], "names": ["PreloadChunks", "moduleIds", "window", "workStore", "workAsyncStorage", "getStore", "undefined", "allFiles", "reactLoadableManifest", "manifest", "key", "chunks", "files", "push", "length", "map", "chunk", "href", "assetPrefix", "encodeURIPath", "isCss", "endsWith", "link", "precedence", "rel", "as", "preload", "fetchPriority"], "mappings": "AAAA;;;;;+BAOg<PERSON>,iBAAAA;;;eAAAA;;;;0BALQ;0CAES;+BACH;AAEvB,SAASA,cAAc,KAI7B;IAJ6B,IAAA,EAC5BC,SAAS,EAGV,GAJ6B;IAK5B,+EAA+E;IAC/E,IAAI,OAAOC,WAAW,aAAa;QACjC,OAAO;IACT;IAEA,MAAMC,YAAYC,0BAAAA,gBAAgB,CAACC,QAAQ;IAC3C,IAAIF,cAAcG,WAAW;QAC3B,OAAO;IACT;IAEA,MAAMC,WAAW,EAAE;IAEnB,4EAA4E;IAC5E,kDAAkD;IAClD,IAAIJ,UAAUK,qBAAqB,IAAIP,WAAW;QAChD,MAAMQ,WAAWN,UAAUK,qBAAqB;QAChD,KAAK,MAAME,OAAOT,UAAW;YAC3B,IAAI,CAACQ,QAAQ,CAACC,IAAI,EAAE;YACpB,MAAMC,SAASF,QAAQ,CAACC,IAAI,CAACE,KAAK;YAClCL,SAASM,IAAI,IAAIF;QACnB;IACF;IAEA,IAAIJ,SAASO,MAAM,KAAK,GAAG;QACzB,OAAO;IACT;IAEA,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAAA,YAAA,QAAA,EAAA;kBACGP,SAASQ,GAAG,CAAC,CAACC;YACb,MAAMC,OAAUd,UAAUe,WAAW,GAAC,YAASC,CAAAA,GAAAA,eAAAA,aAAa,EAACH;YAC7D,MAAMI,QAAQJ,MAAMK,QAAQ,CAAC;YAC7B,wEAAwE;YACxE,0IAA0I;YAC1I,gDAAgD;YAChD,IAAID,OAAO;gBACT,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACE,QAAAA;oBAEC,aAAa;oBACbC,YAAW;oBACXN,MAAMA;oBACNO,KAAI;oBACJC,IAAG;mBALET;YAQX,OAAO;gBACL,kEAAkE;gBAClEU,CAAAA,GAAAA,UAAAA,OAAO,EAACT,MAAM;oBACZQ,IAAI;oBACJE,eAAe;gBACjB;gBACA,OAAO;YACT;QACF;;AAGN", "ignoreList": [0]}}, {"offset": {"line": 535, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 540, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/node_modules/next/src/shared/lib/lazy-dynamic/loadable.tsx"], "sourcesContent": ["import { Suspense, Fragment, lazy } from 'react'\nimport { BailoutToCSR } from './dynamic-bailout-to-csr'\nimport type { ComponentModule } from './types'\nimport { PreloadChunks } from './preload-chunks'\n\n// Normalize loader to return the module as form { default: Component } for `React.lazy`.\n// Also for backward compatible since next/dynamic allows to resolve a component directly with loader\n// Client component reference proxy need to be converted to a module.\nfunction convertModule<P>(\n  mod: React.ComponentType<P> | ComponentModule<P> | undefined\n): {\n  default: React.ComponentType<P>\n} {\n  // Check \"default\" prop before accessing it, as it could be client reference proxy that could break it reference.\n  // Cases:\n  // mod: { default: Component }\n  // mod: Component\n  // mod: { default: proxy(Component) }\n  // mod: proxy(Component)\n  const hasDefault = mod && 'default' in mod\n  return {\n    default: hasDefault\n      ? (mod as ComponentModule<P>).default\n      : (mod as React.ComponentType<P>),\n  }\n}\n\nconst defaultOptions = {\n  loader: () => Promise.resolve(convertModule(() => null)),\n  loading: null,\n  ssr: true,\n}\n\ninterface LoadableOptions {\n  loader?: () => Promise<React.ComponentType<any> | ComponentModule<any>>\n  loading?: React.ComponentType<any> | null\n  ssr?: boolean\n  modules?: string[]\n}\n\nfunction Loadable(options: LoadableOptions) {\n  const opts = { ...defaultOptions, ...options }\n  const Lazy = lazy(() => opts.loader().then(convertModule))\n  const Loading = opts.loading\n\n  function LoadableComponent(props: any) {\n    const fallbackElement = Loading ? (\n      <Loading isLoading={true} pastDelay={true} error={null} />\n    ) : null\n\n    // If it's non-SSR or provided a loading component, wrap it in a suspense boundary\n    const hasSuspenseBoundary = !opts.ssr || !!opts.loading\n    const Wrap = hasSuspenseBoundary ? Suspense : Fragment\n    const wrapProps = hasSuspenseBoundary ? { fallback: fallbackElement } : {}\n    const children = opts.ssr ? (\n      <>\n        {/* During SSR, we need to preload the CSS from the dynamic component to avoid flash of unstyled content */}\n        {typeof window === 'undefined' ? (\n          <PreloadChunks moduleIds={opts.modules} />\n        ) : null}\n        <Lazy {...props} />\n      </>\n    ) : (\n      <BailoutToCSR reason=\"next/dynamic\">\n        <Lazy {...props} />\n      </BailoutToCSR>\n    )\n\n    return <Wrap {...wrapProps}>{children}</Wrap>\n  }\n\n  LoadableComponent.displayName = 'LoadableComponent'\n\n  return LoadableComponent\n}\n\nexport default Loadable\n"], "names": ["convertModule", "mod", "<PERSON><PERSON><PERSON><PERSON>", "default", "defaultOptions", "loader", "Promise", "resolve", "loading", "ssr", "Loadable", "options", "opts", "Lazy", "lazy", "then", "Loading", "LoadableComponent", "props", "fallbackElement", "isLoading", "past<PERSON>elay", "error", "hasSuspenseBoundary", "Wrap", "Suspense", "Fragment", "wrapProps", "fallback", "children", "window", "PreloadChunks", "moduleIds", "modules", "BailoutToCSR", "reason", "displayName"], "mappings": ";;;;+BA4EA,WAAA;;;eAAA;;;;uBA5EyC;qCACZ;+BAEC;AAE9B,yFAAyF;AACzF,qGAAqG;AACrG,qEAAqE;AACrE,SAASA,cACPC,GAA4D;IAI5D,iHAAiH;IACjH,SAAS;IACT,8BAA8B;IAC9B,iBAAiB;IACjB,qCAAqC;IACrC,wBAAwB;IACxB,MAAMC,aAAaD,OAAO,aAAaA;IACvC,OAAO;QACLE,SAASD,aACJD,IAA2BE,OAAO,GAClCF;IACP;AACF;AAEA,MAAMG,iBAAiB;IACrBC,QAAQ,IAAMC,QAAQC,OAAO,CAACP,cAAc,IAAM;IAClDQ,SAAS;IACTC,KAAK;AACP;AASA,SAASC,SAASC,OAAwB;IACxC,MAAMC,OAAO;QAAE,GAAGR,cAAc;QAAE,GAAGO,OAAO;IAAC;IAC7C,MAAME,OAAAA,WAAAA,GAAOC,CAAAA,GAAAA,OAAAA,IAAI,EAAC,IAAMF,KAAKP,MAAM,GAAGU,IAAI,CAACf;IAC3C,MAAMgB,UAAUJ,KAAKJ,OAAO;IAE5B,SAASS,kBAAkBC,KAAU;QACnC,MAAMC,kBAAkBH,UAAAA,WAAAA,GACtB,CAAA,GAAA,YAAA,GAAA,EAACA,SAAAA;YAAQI,WAAW;YAAMC,WAAW;YAAMC,OAAO;aAChD;QAEJ,kFAAkF;QAClF,MAAMC,sBAAsB,CAACX,KAAKH,GAAG,IAAI,CAAC,CAACG,KAAKJ,OAAO;QACvD,MAAMgB,OAAOD,sBAAsBE,OAAAA,QAAQ,GAAGC,OAAAA,QAAQ;QACtD,MAAMC,YAAYJ,sBAAsB;YAAEK,UAAUT;QAAgB,IAAI,CAAC;QACzE,MAAMU,WAAWjB,KAAKH,GAAG,GAAA,WAAA,GACvB,CAAA,GAAA,YAAA,IAAA,EAAA,YAAA,QAAA,EAAA;;gBAEG,OAAOqB,WAAW,cAAA,WAAA,GACjB,CAAA,GAAA,YAAA,GAAA,EAACC,eAAAA,aAAa,EAAA;oBAACC,WAAWpB,KAAKqB,OAAO;qBACpC;8BACJ,CAAA,GAAA,YAAA,GAAA,EAACpB,MAAAA;oBAAM,GAAGK,KAAK;;;2BAGjB,CAAA,GAAA,YAAA,GAAA,EAACgB,qBAAAA,YAAY,EAAA;YAACC,QAAO;sBACnB,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,EAACtB,MAAAA;gBAAM,GAAGK,KAAK;;;QAInB,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAACM,MAAAA;YAAM,GAAGG,SAAS;sBAAGE;;IAC/B;IAEAZ,kBAAkBmB,WAAW,GAAG;IAEhC,OAAOnB;AACT;MAEA,WAAeP", "ignoreList": [0]}}, {"offset": {"line": 617, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 622, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/node_modules/next/src/shared/lib/app-dynamic.tsx"], "sourcesContent": ["import type React from 'react'\nimport type { JSX } from 'react'\nimport Loadable from './lazy-dynamic/loadable'\n\nimport type {\n  LoadableGeneratedOptions,\n  DynamicOptionsLoadingProps,\n  Loader,\n  LoaderComponent,\n} from './lazy-dynamic/types'\n\nexport {\n  type LoadableGeneratedOptions,\n  type DynamicOptionsLoadingProps,\n  type Loader,\n  type LoaderComponent,\n}\n\nexport type DynamicOptions<P = {}> = LoadableGeneratedOptions & {\n  loading?: () => JSX.Element | null\n  loader?: Loader<P>\n  loadableGenerated?: LoadableGeneratedOptions\n  modules?: string[]\n  ssr?: boolean\n}\n\nexport type LoadableOptions<P = {}> = DynamicOptions<P>\n\nexport type LoadableFn<P = {}> = (\n  opts: LoadableOptions<P>\n) => React.ComponentType<P>\n\nexport type LoadableComponent<P = {}> = React.ComponentType<P>\n\nexport default function dynamic<P = {}>(\n  dynamicOptions: DynamicOptions<P> | Loader<P>,\n  options?: DynamicOptions<P>\n): React.ComponentType<P> {\n  const loadableOptions: LoadableOptions<P> = {}\n\n  if (typeof dynamicOptions === 'function') {\n    loadableOptions.loader = dynamicOptions\n  }\n\n  const mergedOptions = {\n    ...loadableOptions,\n    ...options,\n  }\n\n  return Loadable({\n    ...mergedOptions,\n    modules: mergedOptions.loadableGenerated?.modules,\n  })\n}\n"], "names": ["dynamic", "dynamicOptions", "options", "mergedOptions", "loadableOptions", "loader", "Loadable", "modules", "loadableGenerated"], "mappings": ";;;;+BAk<PERSON>,WAAA;;;eAAwBA;;;;mEAhCH;AAgCN,SAASA,QACtBC,cAA6C,EAC7CC,OAA2B;QAehBC;IAbX,MAAMC,kBAAsC,CAAC;IAE7C,IAAI,OAAOH,mBAAmB,YAAY;QACxCG,gBAAgBC,MAAM,GAAGJ;IAC3B;IAEA,MAAME,gBAAgB;QACpB,GAAGC,eAAe;QAClB,GAAGF,OAAO;IACZ;IAEA,OAAOI,CAAAA,GAAAA,UAAAA,OAAQ,EAAC;QACd,GAAGH,aAAa;QAChBI,OAAO,EAAA,CAAEJ,mCAAAA,cAAcK,iBAAiB,KAAA,OAAA,KAAA,IAA/BL,iCAAiCI,OAAO;IACnD;AACF", "ignoreList": [0]}}, {"offset": {"line": 656, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}