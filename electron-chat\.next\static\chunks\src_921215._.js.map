{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/src/hooks/useClientMount.ts"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\n/**\n * Custom hook to handle client-side mounting and prevent hydration mismatches\n * Returns true only after the component has mounted on the client side\n */\nexport function useClientMount() {\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  return mounted;\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAQO,SAAS;;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,WAAW;QACb;mCAAG,EAAE;IAEL,OAAO;AACT;GARgB"}}, {"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, useScroll } from 'framer-motion';\nimport Link from 'next/link';\nimport { useClientMount } from '../hooks/useClientMount';\n\nexport default function Header() {\n  const mounted = useClientMount();\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const { scrollY } = useScroll();\n\n  useEffect(() => {\n    if (!mounted) return;\n\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, [mounted]);\n\n  const navItems = [\n    { name: 'Solutions', href: '#ai-content', icon: '🧠' },\n    { name: 'Technology', href: '#machine-learning', icon: '⚡' },\n    { name: 'About', href: '#about', icon: '🚀' },\n    { name: 'Contact', href: '#contact', icon: '💬' },\n  ];\n\n  if (!mounted) {\n    return (\n      <header className=\"fixed top-0 left-0 right-0 z-50\">\n        <nav className=\"container mx-auto px-6 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"text-2xl font-bold gradient-text-primary\">\n              Hydra AI\n            </div>\n            <div className=\"hidden md:flex items-center space-x-8\">\n              {navItems.map((item) => (\n                <div key={item.name}>\n                  <span className=\"text-gray-300\">{item.name}</span>\n                </div>\n              ))}\n            </div>\n            <button className=\"hidden md:block px-6 py-2 rounded-full btn-primary\">\n              Get Started\n            </button>\n          </div>\n        </nav>\n      </header>\n    );\n  }\n\n  return (\n    <motion.header\n      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${\n        isScrolled\n          ? 'glass-strong shadow-2xl shadow-purple-500/10'\n          : 'bg-transparent'\n      }`}\n      initial={{ y: -100, opacity: 0 }}\n      animate={{ y: 0, opacity: 1 }}\n      transition={{ type: 'spring', stiffness: 100, damping: 20 }}\n    >\n      <nav className=\"container mx-auto px-6 py-4\">\n        <div className=\"flex items-center justify-between\">\n          {/* Logo */}\n          <motion.div\n            className=\"flex items-center space-x-3\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <div className=\"w-10 h-10 rounded-xl glass flex items-center justify-center\">\n              <span className=\"text-2xl\">🧠</span>\n            </div>\n            <div className=\"text-2xl font-bold gradient-text-primary\">\n              Hydra AI\n            </div>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-1\">\n            {navItems.map((item, index) => (\n              <motion.div\n                key={item.name}\n                initial={{ opacity: 0, y: -20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: index * 0.1 }}\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                <Link\n                  href={item.href}\n                  className=\"flex items-center space-x-2 px-4 py-2 rounded-xl text-gray-300 hover:text-white transition-all duration-300 hover:bg-white/5 group\"\n                  onClick={(e) => {\n                    e.preventDefault();\n                    const element = document.querySelector(item.href);\n                    if (element) {\n                      element.scrollIntoView({ behavior: 'smooth' });\n                    }\n                  }}\n                >\n                  <span className=\"text-sm group-hover:scale-110 transition-transform\">\n                    {item.icon}\n                  </span>\n                  <span className=\"font-medium\">{item.name}</span>\n                </Link>\n              </motion.div>\n            ))}\n          </div>\n\n          {/* CTA Button */}\n          <motion.button\n            className=\"hidden md:flex items-center space-x-2 px-6 py-3 rounded-xl btn-primary font-semibold shadow-lg hover:shadow-xl hover:shadow-purple-500/25 transition-all duration-300\"\n            whileHover={{ scale: 1.05, y: -2 }}\n            whileTap={{ scale: 0.95 }}\n            initial={{ opacity: 0, x: 20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ delay: 0.5 }}\n          >\n            <span>Get Started</span>\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 7l5 5m0 0l-5 5m5-5H6\" />\n            </svg>\n          </motion.button>\n\n          {/* Mobile Menu Button */}\n          <motion.button\n            className=\"md:hidden p-2 rounded-xl glass text-white\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n          >\n            <motion.svg\n              className=\"w-6 h-6\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n              animate={{ rotate: isMobileMenuOpen ? 90 : 0 }}\n              transition={{ duration: 0.3 }}\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d={isMobileMenuOpen ? \"M6 18L18 6M6 6l12 12\" : \"M4 6h16M4 12h16M4 18h16\"}\n              />\n            </motion.svg>\n          </motion.button>\n        </div>\n\n        {/* Mobile Menu */}\n        <motion.div\n          className=\"md:hidden overflow-hidden\"\n          initial={{ height: 0, opacity: 0 }}\n          animate={{\n            height: isMobileMenuOpen ? 'auto' : 0,\n            opacity: isMobileMenuOpen ? 1 : 0\n          }}\n          transition={{ duration: 0.3 }}\n        >\n          <div className=\"py-4 space-y-2\">\n            {navItems.map((item, index) => (\n              <motion.div\n                key={item.name}\n                initial={{ opacity: 0, x: -20 }}\n                animate={{\n                  opacity: isMobileMenuOpen ? 1 : 0,\n                  x: isMobileMenuOpen ? 0 : -20\n                }}\n                transition={{ delay: index * 0.1 }}\n              >\n                <Link\n                  href={item.href}\n                  className=\"flex items-center space-x-3 px-4 py-3 rounded-xl text-gray-300 hover:text-white transition-all duration-300 hover:bg-white/5\"\n                  onClick={(e) => {\n                    e.preventDefault();\n                    setIsMobileMenuOpen(false);\n                    const element = document.querySelector(item.href);\n                    if (element) {\n                      element.scrollIntoView({ behavior: 'smooth' });\n                    }\n                  }}\n                >\n                  <span className=\"text-lg\">{item.icon}</span>\n                  <span className=\"font-medium\">{item.name}</span>\n                </Link>\n              </motion.div>\n            ))}\n            <motion.button\n              className=\"w-full mt-4 px-4 py-3 rounded-xl btn-primary font-semibold\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{\n                opacity: isMobileMenuOpen ? 1 : 0,\n                y: isMobileMenuOpen ? 0 : 20\n              }}\n              transition={{ delay: 0.4 }}\n            >\n              Get Started\n            </motion.button>\n          </div>\n        </motion.div>\n      </nav>\n    </motion.header>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAFA;AAAA;;;AAHA;;;;;AAOe,SAAS;;IACtB,MAAM,UAAU,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAC7B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,IAAI,CAAC,SAAS;YAEd,MAAM;iDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG;QAAC;KAAQ;IAEZ,MAAM,WAAW;QACf;YAAE,MAAM;YAAa,MAAM;YAAe,MAAM;QAAK;QACrD;YAAE,MAAM;YAAc,MAAM;YAAqB,MAAM;QAAI;QAC3D;YAAE,MAAM;YAAS,MAAM;YAAU,MAAM;QAAK;QAC5C;YAAE,MAAM;YAAW,MAAM;YAAY,MAAM;QAAK;KACjD;IAED,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAO,WAAU;sBAChB,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAA2C;;;;;;sCAG1D,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC;8CACC,cAAA,6LAAC;wCAAK,WAAU;kDAAiB,KAAK,IAAI;;;;;;mCADlC,KAAK,IAAI;;;;;;;;;;sCAKvB,6LAAC;4BAAO,WAAU;sCAAqD;;;;;;;;;;;;;;;;;;;;;;IAOjF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,WAAW,CAAC,4DAA4D,EACtE,aACI,iDACA,kBACJ;QACF,SAAS;YAAE,GAAG,CAAC;YAAK,SAAS;QAAE;QAC/B,SAAS;YAAE,GAAG;YAAG,SAAS;QAAE;QAC5B,YAAY;YAAE,MAAM;YAAU,WAAW;YAAK,SAAS;QAAG;kBAE1D,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;;8CAExB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAAW;;;;;;;;;;;8CAE7B,6LAAC;oCAAI,WAAU;8CAA2C;;;;;;;;;;;;sCAM5D,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,QAAQ;oCAAI;oCACjC,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;8CAExB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,WAAU;wCACV,SAAS,CAAC;4CACR,EAAE,cAAc;4CAChB,MAAM,UAAU,SAAS,aAAa,CAAC,KAAK,IAAI;4CAChD,IAAI,SAAS;gDACX,QAAQ,cAAc,CAAC;oDAAE,UAAU;gDAAS;4CAC9C;wCACF;;0DAEA,6LAAC;gDAAK,WAAU;0DACb,KAAK,IAAI;;;;;;0DAEZ,6LAAC;gDAAK,WAAU;0DAAe,KAAK,IAAI;;;;;;;;;;;;mCArBrC,KAAK,IAAI;;;;;;;;;;sCA4BpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,WAAU;4BACV,YAAY;gCAAE,OAAO;gCAAM,GAAG,CAAC;4BAAE;4BACjC,UAAU;gCAAE,OAAO;4BAAK;4BACxB,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;;8CAEzB,6LAAC;8CAAK;;;;;;8CACN,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;sCAKzE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,WAAU;4BACV,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;4BACxB,SAAS,IAAM,oBAAoB,CAAC;sCAEpC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,MAAK;gCACL,QAAO;gCACP,SAAQ;gCACR,SAAS;oCAAE,QAAQ,mBAAmB,KAAK;gCAAE;gCAC7C,YAAY;oCAAE,UAAU;gCAAI;0CAE5B,cAAA,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAG,mBAAmB,yBAAyB;;;;;;;;;;;;;;;;;;;;;;8BAOvD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,QAAQ;wBAAG,SAAS;oBAAE;oBACjC,SAAS;wBACP,QAAQ,mBAAmB,SAAS;wBACpC,SAAS,mBAAmB,IAAI;oBAClC;oBACA,YAAY;wBAAE,UAAU;oBAAI;8BAE5B,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCACP,SAAS,mBAAmB,IAAI;wCAChC,GAAG,mBAAmB,IAAI,CAAC;oCAC7B;oCACA,YAAY;wCAAE,OAAO,QAAQ;oCAAI;8CAEjC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,WAAU;wCACV,SAAS,CAAC;4CACR,EAAE,cAAc;4CAChB,oBAAoB;4CACpB,MAAM,UAAU,SAAS,aAAa,CAAC,KAAK,IAAI;4CAChD,IAAI,SAAS;gDACX,QAAQ,cAAc,CAAC;oDAAE,UAAU;gDAAS;4CAC9C;wCACF;;0DAEA,6LAAC;gDAAK,WAAU;0DAAW,KAAK,IAAI;;;;;;0DACpC,6LAAC;gDAAK,WAAU;0DAAe,KAAK,IAAI;;;;;;;;;;;;mCArBrC,KAAK,IAAI;;;;;0CAyBlB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCACP,SAAS,mBAAmB,IAAI;oCAChC,GAAG,mBAAmB,IAAI;gCAC5B;gCACA,YAAY;oCAAE,OAAO;gCAAI;0CAC1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAvMwB;;QACN,iIAAA,CAAA,iBAAc;QAGV,4KAAA,CAAA,YAAS;;;KAJP"}}, {"offset": {"line": 503, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}