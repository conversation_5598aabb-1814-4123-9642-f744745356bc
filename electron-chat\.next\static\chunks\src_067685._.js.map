{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/src/hooks/useClientMount.ts"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\n/**\n * Custom hook to handle client-side mounting and prevent hydration mismatches\n * Returns true only after the component has mounted on the client side\n */\nexport function useClientMount() {\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  return mounted;\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAQO,SAAS;;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,WAAW;QACb;mCAAG,EAAE;IAEL,OAAO;AACT;GARgB"}}, {"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/src/components/Background.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useClientMount } from '../hooks/useClientMount';\n\ninterface FloatingElement {\n  id: number;\n  x: number;\n  y: number;\n  size: number;\n  color: string;\n  type: 'circle' | 'hexagon' | 'triangle';\n  opacity: number;\n  duration: number;\n  delay: number;\n}\n\nexport default function Background() {\n  const mounted = useClientMount();\n\n  const floatingElements: FloatingElement[] = [\n    { id: 1, x: 15, y: 20, size: 80, color: '#667eea', type: 'circle', opacity: 0.1, duration: 12, delay: 0 },\n    { id: 2, x: 75, y: 15, size: 60, color: '#764ba2', type: 'circle', opacity: 0.08, duration: 15, delay: 5 },\n    { id: 3, x: 85, y: 70, size: 70, color: '#f093fb', type: 'circle', opacity: 0.06, duration: 18, delay: 10 },\n  ];\n\n  const gridLines = Array.from({ length: 20 }, (_, i) => i);\n\n  if (!mounted) {\n    return (\n      <div className=\"fixed inset-0 -z-10 overflow-hidden\">\n        <div className=\"absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900/20 to-slate-900\" />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"fixed inset-0 -z-10 overflow-hidden\">\n      {/* Animated gradient background */}\n      <motion.div\n        className=\"absolute inset-0\"\n        style={{\n          background: 'radial-gradient(ellipse at top, #1a1a2e 0%, #16213e 50%, #0f0f23 100%)',\n        }}\n        animate={{\n          background: [\n            'radial-gradient(ellipse at top, #1a1a2e 0%, #16213e 50%, #0f0f23 100%)',\n            'radial-gradient(ellipse at bottom right, #1a1a2e 0%, #16213e 50%, #0f0f23 100%)',\n            'radial-gradient(ellipse at top left, #1a1a2e 0%, #16213e 50%, #0f0f23 100%)',\n            'radial-gradient(ellipse at top, #1a1a2e 0%, #16213e 50%, #0f0f23 100%)',\n          ],\n        }}\n        transition={{\n          duration: 30,\n          repeat: Infinity,\n          ease: 'linear',\n        }}\n      />\n\n      {/* Grid pattern */}\n      <div className=\"absolute inset-0 opacity-[0.03]\">\n        <svg width=\"100%\" height=\"100%\" className=\"absolute inset-0\">\n          <defs>\n            <pattern id=\"grid\" width=\"50\" height=\"50\" patternUnits=\"userSpaceOnUse\">\n              <path d=\"M 50 0 L 0 0 0 50\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"1\"/>\n            </pattern>\n          </defs>\n          <rect width=\"100%\" height=\"100%\" fill=\"url(#grid)\" />\n        </svg>\n      </div>\n\n      {/* Floating elements */}\n      {floatingElements.map((element) => (\n        <motion.div\n          key={element.id}\n          className=\"absolute\"\n          initial={{\n            x: `${element.x}%`,\n            y: `${element.y}%`,\n            opacity: 0,\n            scale: 0.5,\n          }}\n          animate={{\n            opacity: [element.opacity * 0.5, element.opacity, element.opacity * 0.5],\n            scale: [0.9, 1, 0.9],\n          }}\n          transition={{\n            duration: element.duration,\n            repeat: Infinity,\n            ease: 'easeInOut',\n            delay: element.delay,\n          }}\n          style={{\n            width: element.size,\n            height: element.size,\n          }}\n        >\n          <div\n            className=\"w-full h-full rounded-full\"\n            style={{\n              background: `radial-gradient(circle at center, ${element.color}30, transparent)`,\n              filter: 'blur(30px)',\n            }}\n          />\n        </motion.div>\n      ))}\n\n\n\n      {/* Static ambient light effects */}\n      <div\n        className=\"absolute top-0 left-1/4 w-96 h-96 rounded-full opacity-20\"\n        style={{\n          background: 'radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%)',\n          filter: 'blur(60px)',\n        }}\n      />\n\n      <div\n        className=\"absolute bottom-0 right-1/4 w-80 h-80 rounded-full opacity-30\"\n        style={{\n          background: 'radial-gradient(circle, rgba(118, 75, 162, 0.15) 0%, transparent 70%)',\n          filter: 'blur(50px)',\n        }}\n      />\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAGA;AADA;;;AAFA;;;AAiBe,SAAS;;IACtB,MAAM,UAAU,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAE7B,MAAM,mBAAsC;QAC1C;YAAE,IAAI;YAAG,GAAG;YAAI,GAAG;YAAI,MAAM;YAAI,OAAO;YAAW,MAAM;YAAU,SAAS;YAAK,UAAU;YAAI,OAAO;QAAE;QACxG;YAAE,IAAI;YAAG,GAAG;YAAI,GAAG;YAAI,MAAM;YAAI,OAAO;YAAW,MAAM;YAAU,SAAS;YAAM,UAAU;YAAI,OAAO;QAAE;QACzG;YAAE,IAAI;YAAG,GAAG;YAAI,GAAG;YAAI,MAAM;YAAI,OAAO;YAAW,MAAM;YAAU,SAAS;YAAM,UAAU;YAAI,OAAO;QAAG;KAC3G;IAED,MAAM,YAAY,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAG,GAAG,CAAC,GAAG,IAAM;IAEvD,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL,YAAY;gBACd;gBACA,SAAS;oBACP,YAAY;wBACV;wBACA;wBACA;wBACA;qBACD;gBACH;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;;;;;;0BAIF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,OAAM;oBAAO,QAAO;oBAAO,WAAU;;sCACxC,6LAAC;sCACC,cAAA,6LAAC;gCAAQ,IAAG;gCAAO,OAAM;gCAAK,QAAO;gCAAK,cAAa;0CACrD,cAAA,6LAAC;oCAAK,GAAE;oCAAoB,MAAK;oCAAO,QAAO;oCAAe,aAAY;;;;;;;;;;;;;;;;sCAG9E,6LAAC;4BAAK,OAAM;4BAAO,QAAO;4BAAO,MAAK;;;;;;;;;;;;;;;;;YAKzC,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAU;oBACV,SAAS;wBACP,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;wBAClB,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;wBAClB,SAAS;wBACT,OAAO;oBACT;oBACA,SAAS;wBACP,SAAS;4BAAC,QAAQ,OAAO,GAAG;4BAAK,QAAQ,OAAO;4BAAE,QAAQ,OAAO,GAAG;yBAAI;wBACxE,OAAO;4BAAC;4BAAK;4BAAG;yBAAI;oBACtB;oBACA,YAAY;wBACV,UAAU,QAAQ,QAAQ;wBAC1B,QAAQ;wBACR,MAAM;wBACN,OAAO,QAAQ,KAAK;oBACtB;oBACA,OAAO;wBACL,OAAO,QAAQ,IAAI;wBACnB,QAAQ,QAAQ,IAAI;oBACtB;8BAEA,cAAA,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,YAAY,CAAC,kCAAkC,EAAE,QAAQ,KAAK,CAAC,gBAAgB,CAAC;4BAChF,QAAQ;wBACV;;;;;;mBA5BG,QAAQ,EAAE;;;;;0BAoCnB,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,YAAY;oBACZ,QAAQ;gBACV;;;;;;0BAGF,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,YAAY;oBACZ,QAAQ;gBACV;;;;;;;;;;;;AAIR;GA9GwB;;QACN,iIAAA,CAAA,iBAAc;;;KADR"}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}