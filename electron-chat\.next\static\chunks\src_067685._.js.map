{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/src/hooks/useClientMount.ts"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\n/**\n * Custom hook to handle client-side mounting and prevent hydration mismatches\n * Returns true only after the component has mounted on the client side\n */\nexport function useClientMount() {\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  return mounted;\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAQO,SAAS;;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,WAAW;QACb;mCAAG,EAAE;IAEL,OAAO;AACT;GARgB"}}, {"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/src/components/Background.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useClientMount } from '../hooks/useClientMount';\n\ninterface FloatingElement {\n  id: number;\n  x: number;\n  y: number;\n  size: number;\n  color: string;\n  type: 'circle' | 'hexagon' | 'triangle';\n  opacity: number;\n  duration: number;\n  delay: number;\n}\n\nexport default function Background() {\n  const mounted = useClientMount();\n\n  const floatingElements: FloatingElement[] = [\n    { id: 1, x: 15, y: 20, size: 120, color: '#667eea', type: 'circle', opacity: 0.15, duration: 8, delay: 0 },\n    { id: 2, x: 75, y: 15, size: 80, color: '#764ba2', type: 'hexagon', opacity: 0.12, duration: 12, delay: 2 },\n    { id: 3, x: 85, y: 70, size: 100, color: '#f093fb', type: 'circle', opacity: 0.1, duration: 10, delay: 4 },\n    { id: 4, x: 25, y: 80, size: 60, color: '#4facfe', type: 'triangle', opacity: 0.18, duration: 15, delay: 1 },\n    { id: 5, x: 60, y: 45, size: 140, color: '#00f2fe', type: 'circle', opacity: 0.08, duration: 20, delay: 6 },\n    { id: 6, x: 5, y: 60, size: 90, color: '#f5576c', type: 'hexagon', opacity: 0.14, duration: 14, delay: 3 },\n  ];\n\n  const gridLines = Array.from({ length: 20 }, (_, i) => i);\n\n  if (!mounted) {\n    return (\n      <div className=\"fixed inset-0 -z-10 overflow-hidden\">\n        <div className=\"absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900/20 to-slate-900\" />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"fixed inset-0 -z-10 overflow-hidden\">\n      {/* Animated gradient background */}\n      <motion.div\n        className=\"absolute inset-0\"\n        style={{\n          background: 'radial-gradient(ellipse at top, #1a1a2e 0%, #16213e 50%, #0f0f23 100%)',\n        }}\n        animate={{\n          background: [\n            'radial-gradient(ellipse at top, #1a1a2e 0%, #16213e 50%, #0f0f23 100%)',\n            'radial-gradient(ellipse at bottom right, #1a1a2e 0%, #16213e 50%, #0f0f23 100%)',\n            'radial-gradient(ellipse at top left, #1a1a2e 0%, #16213e 50%, #0f0f23 100%)',\n            'radial-gradient(ellipse at top, #1a1a2e 0%, #16213e 50%, #0f0f23 100%)',\n          ],\n        }}\n        transition={{\n          duration: 30,\n          repeat: Infinity,\n          ease: 'linear',\n        }}\n      />\n\n      {/* Grid pattern */}\n      <div className=\"absolute inset-0 opacity-[0.03]\">\n        <svg width=\"100%\" height=\"100%\" className=\"absolute inset-0\">\n          <defs>\n            <pattern id=\"grid\" width=\"50\" height=\"50\" patternUnits=\"userSpaceOnUse\">\n              <path d=\"M 50 0 L 0 0 0 50\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"1\"/>\n            </pattern>\n          </defs>\n          <rect width=\"100%\" height=\"100%\" fill=\"url(#grid)\" />\n        </svg>\n      </div>\n\n      {/* Floating elements */}\n      {floatingElements.map((element) => (\n        <motion.div\n          key={element.id}\n          className=\"absolute\"\n          initial={{\n            x: `${element.x}%`,\n            y: `${element.y}%`,\n            opacity: 0,\n            scale: 0.5,\n          }}\n          animate={{\n            x: [`${element.x}%`, `${element.x + 2}%`, `${element.x - 2}%`, `${element.x}%`],\n            y: [`${element.y}%`, `${element.y - 3}%`, `${element.y + 3}%`, `${element.y}%`],\n            opacity: [element.opacity * 0.5, element.opacity, element.opacity, element.opacity * 0.5],\n            scale: [0.8, 1, 0.9, 0.8],\n            rotate: [0, 90, 180],\n          }}\n          transition={{\n            duration: element.duration,\n            repeat: Infinity,\n            ease: 'easeInOut',\n            delay: element.delay,\n          }}\n          style={{\n            width: element.size,\n            height: element.size,\n          }}\n        >\n          {element.type === 'circle' && (\n            <div\n              className=\"w-full h-full rounded-full\"\n              style={{\n                background: `radial-gradient(circle at 30% 30%, ${element.color}40, ${element.color}10, transparent)`,\n                filter: 'blur(20px)',\n              }}\n            />\n          )}\n          {element.type === 'hexagon' && (\n            <div\n              className=\"w-full h-full\"\n              style={{\n                clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)',\n                background: `linear-gradient(135deg, ${element.color}30, ${element.color}10)`,\n                filter: 'blur(15px)',\n              }}\n            />\n          )}\n          {element.type === 'triangle' && (\n            <div\n              className=\"w-full h-full\"\n              style={{\n                clipPath: 'polygon(50% 0%, 0% 100%, 100% 100%)',\n                background: `linear-gradient(45deg, ${element.color}25, ${element.color}05)`,\n                filter: 'blur(12px)',\n              }}\n            />\n          )}\n        </motion.div>\n      ))}\n\n      {/* Subtle particle system */}\n      <div className=\"absolute inset-0\">\n        {Array.from({ length: 20 }, (_, i) => (\n          <motion.div\n            key={`particle-${i}`}\n            className=\"absolute w-0.5 h-0.5 bg-white rounded-full\"\n            initial={{\n              x: Math.random() * (typeof window !== 'undefined' ? window.innerWidth : 1200),\n              y: Math.random() * (typeof window !== 'undefined' ? window.innerHeight : 800),\n              opacity: 0,\n            }}\n            animate={{\n              y: [null, -50],\n              opacity: [0, 0.3, 0],\n            }}\n            transition={{\n              duration: Math.random() * 15 + 15,\n              repeat: Infinity,\n              delay: Math.random() * 10,\n              ease: 'linear',\n            }}\n          />\n        ))}\n      </div>\n\n      {/* Ambient light effects */}\n      <motion.div\n        className=\"absolute top-0 left-1/4 w-96 h-96 rounded-full\"\n        style={{\n          background: 'radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%)',\n          filter: 'blur(60px)',\n        }}\n        animate={{\n          scale: [1, 1.2, 1],\n          opacity: [0.3, 0.6, 0.3],\n        }}\n        transition={{\n          duration: 8,\n          repeat: Infinity,\n          ease: 'easeInOut',\n        }}\n      />\n\n      <motion.div\n        className=\"absolute bottom-0 right-1/4 w-80 h-80 rounded-full\"\n        style={{\n          background: 'radial-gradient(circle, rgba(118, 75, 162, 0.15) 0%, transparent 70%)',\n          filter: 'blur(50px)',\n        }}\n        animate={{\n          scale: [1, 0.8, 1],\n          opacity: [0.4, 0.7, 0.4],\n        }}\n        transition={{\n          duration: 12,\n          repeat: Infinity,\n          ease: 'easeInOut',\n          delay: 2,\n        }}\n      />\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAGA;AADA;;;AAFA;;;AAiBe,SAAS;;IACtB,MAAM,UAAU,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAE7B,MAAM,mBAAsC;QAC1C;YAAE,IAAI;YAAG,GAAG;YAAI,GAAG;YAAI,MAAM;YAAK,OAAO;YAAW,MAAM;YAAU,SAAS;YAAM,UAAU;YAAG,OAAO;QAAE;QACzG;YAAE,IAAI;YAAG,GAAG;YAAI,GAAG;YAAI,MAAM;YAAI,OAAO;YAAW,MAAM;YAAW,SAAS;YAAM,UAAU;YAAI,OAAO;QAAE;QAC1G;YAAE,IAAI;YAAG,GAAG;YAAI,GAAG;YAAI,MAAM;YAAK,OAAO;YAAW,MAAM;YAAU,SAAS;YAAK,UAAU;YAAI,OAAO;QAAE;QACzG;YAAE,IAAI;YAAG,GAAG;YAAI,GAAG;YAAI,MAAM;YAAI,OAAO;YAAW,MAAM;YAAY,SAAS;YAAM,UAAU;YAAI,OAAO;QAAE;QAC3G;YAAE,IAAI;YAAG,GAAG;YAAI,GAAG;YAAI,MAAM;YAAK,OAAO;YAAW,MAAM;YAAU,SAAS;YAAM,UAAU;YAAI,OAAO;QAAE;QAC1G;YAAE,IAAI;YAAG,GAAG;YAAG,GAAG;YAAI,MAAM;YAAI,OAAO;YAAW,MAAM;YAAW,SAAS;YAAM,UAAU;YAAI,OAAO;QAAE;KAC1G;IAED,MAAM,YAAY,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAG,GAAG,CAAC,GAAG,IAAM;IAEvD,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL,YAAY;gBACd;gBACA,SAAS;oBACP,YAAY;wBACV;wBACA;wBACA;wBACA;qBACD;gBACH;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;;;;;;0BAIF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,OAAM;oBAAO,QAAO;oBAAO,WAAU;;sCACxC,6LAAC;sCACC,cAAA,6LAAC;gCAAQ,IAAG;gCAAO,OAAM;gCAAK,QAAO;gCAAK,cAAa;0CACrD,cAAA,6LAAC;oCAAK,GAAE;oCAAoB,MAAK;oCAAO,QAAO;oCAAe,aAAY;;;;;;;;;;;;;;;;sCAG9E,6LAAC;4BAAK,OAAM;4BAAO,QAAO;4BAAO,MAAK;;;;;;;;;;;;;;;;;YAKzC,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAU;oBACV,SAAS;wBACP,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;wBAClB,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;wBAClB,SAAS;wBACT,OAAO;oBACT;oBACA,SAAS;wBACP,GAAG;4BAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;4BAAE,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC;4BAAE,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC;4BAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;yBAAC;wBAC/E,GAAG;4BAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;4BAAE,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC;4BAAE,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC;4BAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;yBAAC;wBAC/E,SAAS;4BAAC,QAAQ,OAAO,GAAG;4BAAK,QAAQ,OAAO;4BAAE,QAAQ,OAAO;4BAAE,QAAQ,OAAO,GAAG;yBAAI;wBACzF,OAAO;4BAAC;4BAAK;4BAAG;4BAAK;yBAAI;wBACzB,QAAQ;4BAAC;4BAAG;4BAAI;yBAAI;oBACtB;oBACA,YAAY;wBACV,UAAU,QAAQ,QAAQ;wBAC1B,QAAQ;wBACR,MAAM;wBACN,OAAO,QAAQ,KAAK;oBACtB;oBACA,OAAO;wBACL,OAAO,QAAQ,IAAI;wBACnB,QAAQ,QAAQ,IAAI;oBACtB;;wBAEC,QAAQ,IAAI,KAAK,0BAChB,6LAAC;4BACC,WAAU;4BACV,OAAO;gCACL,YAAY,CAAC,mCAAmC,EAAE,QAAQ,KAAK,CAAC,IAAI,EAAE,QAAQ,KAAK,CAAC,gBAAgB,CAAC;gCACrG,QAAQ;4BACV;;;;;;wBAGH,QAAQ,IAAI,KAAK,2BAChB,6LAAC;4BACC,WAAU;4BACV,OAAO;gCACL,UAAU;gCACV,YAAY,CAAC,wBAAwB,EAAE,QAAQ,KAAK,CAAC,IAAI,EAAE,QAAQ,KAAK,CAAC,GAAG,CAAC;gCAC7E,QAAQ;4BACV;;;;;;wBAGH,QAAQ,IAAI,KAAK,4BAChB,6LAAC;4BACC,WAAU;4BACV,OAAO;gCACL,UAAU;gCACV,YAAY,CAAC,uBAAuB,EAAE,QAAQ,KAAK,CAAC,IAAI,EAAE,QAAQ,KAAK,CAAC,GAAG,CAAC;gCAC5E,QAAQ;4BACV;;;;;;;mBApDC,QAAQ,EAAE;;;;;0BA2DnB,6LAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAG,GAAG,CAAC,GAAG,kBAC9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,WAAU;wBACV,SAAS;4BACP,GAAG,KAAK,MAAM,KAAK,CAAC,uCAAgC,OAAO,UAAU,uCAAO;4BAC5E,GAAG,KAAK,MAAM,KAAK,CAAC,uCAAgC,OAAO,WAAW,uCAAM;4BAC5E,SAAS;wBACX;wBACA,SAAS;4BACP,GAAG;gCAAC;gCAAM,CAAC;6BAAG;4BACd,SAAS;gCAAC;gCAAG;gCAAK;6BAAE;wBACtB;wBACA,YAAY;4BACV,UAAU,KAAK,MAAM,KAAK,KAAK;4BAC/B,QAAQ;4BACR,OAAO,KAAK,MAAM,KAAK;4BACvB,MAAM;wBACR;uBAhBK,CAAC,SAAS,EAAE,GAAG;;;;;;;;;;0BAsB1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL,YAAY;oBACZ,QAAQ;gBACV;gBACA,SAAS;oBACP,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;oBAClB,SAAS;wBAAC;wBAAK;wBAAK;qBAAI;gBAC1B;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;;;;;;0BAGF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL,YAAY;oBACZ,QAAQ;gBACV;gBACA,SAAS;oBACP,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;oBAClB,SAAS;wBAAC;wBAAK;wBAAK;qBAAI;gBAC1B;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;oBACN,OAAO;gBACT;;;;;;;;;;;;AAIR;GApLwB;;QACN,iIAAA,CAAA,iBAAc;;;KADR"}}, {"offset": {"line": 418, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}