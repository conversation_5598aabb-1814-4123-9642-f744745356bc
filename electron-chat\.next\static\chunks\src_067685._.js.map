{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/src/hooks/useClientMount.ts"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\n/**\n * Custom hook to handle client-side mounting and prevent hydration mismatches\n * Returns true only after the component has mounted on the client side\n */\nexport function useClientMount() {\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  return mounted;\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAQO,SAAS;;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,WAAW;QACb;mCAAG,EAAE;IAEL,OAAO;AACT;GARgB"}}, {"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/H/electron-chat/src/components/Background.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useClientMount } from '../hooks/useClientMount';\n\ninterface FloatingElement {\n  id: number;\n  x: number;\n  y: number;\n  size: number;\n  color: string;\n  type: 'circle' | 'hexagon' | 'triangle';\n  opacity: number;\n  duration: number;\n  delay: number;\n}\n\nexport default function Background() {\n  const mounted = useClientMount();\n\n  const floatingElements: FloatingElement[] = [\n    { id: 1, x: 15, y: 20, size: 80, color: '#667eea', type: 'circle', opacity: 0.1, duration: 12, delay: 0 },\n    { id: 2, x: 75, y: 15, size: 60, color: '#764ba2', type: 'circle', opacity: 0.08, duration: 15, delay: 5 },\n    { id: 3, x: 85, y: 70, size: 70, color: '#f093fb', type: 'circle', opacity: 0.06, duration: 18, delay: 10 },\n  ];\n\n  const gridLines = Array.from({ length: 20 }, (_, i) => i);\n\n  if (!mounted) {\n    return (\n      <div className=\"fixed inset-0 -z-10 overflow-hidden\">\n        <div className=\"absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900/20 to-slate-900\" />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"fixed inset-0 -z-10 overflow-hidden\">\n      {/* Static gradient background */}\n      <div\n        className=\"absolute inset-0\"\n        style={{\n          background: 'radial-gradient(ellipse at top, #1a1a2e 0%, #16213e 50%, #0f0f23 100%)',\n        }}\n      />\n\n\n\n      {/* Static floating elements */}\n      {floatingElements.map((element) => (\n        <div\n          key={element.id}\n          className=\"absolute\"\n          style={{\n            left: `${element.x}%`,\n            top: `${element.y}%`,\n            width: element.size,\n            height: element.size,\n            opacity: element.opacity,\n          }}\n        >\n          <div\n            className=\"w-full h-full rounded-full\"\n            style={{\n              background: `radial-gradient(circle at center, ${element.color}15, transparent)`,\n              filter: 'blur(10px)',\n            }}\n          />\n        </div>\n      ))}\n\n\n\n      {/* Static ambient light effects */}\n      <div\n        className=\"absolute top-0 left-1/4 w-96 h-96 rounded-full opacity-10\"\n        style={{\n          background: 'radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%)',\n          filter: 'blur(40px)',\n        }}\n      />\n\n      <div\n        className=\"absolute bottom-0 right-1/4 w-80 h-80 rounded-full opacity-15\"\n        style={{\n          background: 'radial-gradient(circle, rgba(118, 75, 162, 0.15) 0%, transparent 70%)',\n          filter: 'blur(30px)',\n        }}\n      />\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAGA;;;AAHA;;AAiBe,SAAS;;IACtB,MAAM,UAAU,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAE7B,MAAM,mBAAsC;QAC1C;YAAE,IAAI;YAAG,GAAG;YAAI,GAAG;YAAI,MAAM;YAAI,OAAO;YAAW,MAAM;YAAU,SAAS;YAAK,UAAU;YAAI,OAAO;QAAE;QACxG;YAAE,IAAI;YAAG,GAAG;YAAI,GAAG;YAAI,MAAM;YAAI,OAAO;YAAW,MAAM;YAAU,SAAS;YAAM,UAAU;YAAI,OAAO;QAAE;QACzG;YAAE,IAAI;YAAG,GAAG;YAAI,GAAG;YAAI,MAAM;YAAI,OAAO;YAAW,MAAM;YAAU,SAAS;YAAM,UAAU;YAAI,OAAO;QAAG;KAC3G;IAED,MAAM,YAAY,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAG,GAAG,CAAC,GAAG,IAAM;IAEvD,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,YAAY;gBACd;;;;;;YAMD,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC;oBAEC,WAAU;oBACV,OAAO;wBACL,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;wBACrB,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;wBACpB,OAAO,QAAQ,IAAI;wBACnB,QAAQ,QAAQ,IAAI;wBACpB,SAAS,QAAQ,OAAO;oBAC1B;8BAEA,cAAA,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,YAAY,CAAC,kCAAkC,EAAE,QAAQ,KAAK,CAAC,gBAAgB,CAAC;4BAChF,QAAQ;wBACV;;;;;;mBAfG,QAAQ,EAAE;;;;;0BAuBnB,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,YAAY;oBACZ,QAAQ;gBACV;;;;;;0BAGF,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,YAAY;oBACZ,QAAQ;gBACV;;;;;;;;;;;;AAIR;GA1EwB;;QACN,iIAAA,CAAA,iBAAc;;;KADR"}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}