/**
 * Utility functions to handle browser extension interference
 */

export function isBrowserExtensionPresent(): boolean {
  if (typeof window === 'undefined') return false;
  
  // Check for common browser extension attributes
  const extensionAttributes = [
    'bis_skin_checked',
    'webcrx',
    '__processed_0561e652-f089-4c1b-88da-52458fca1d20__',
    'data-extension-id',
  ];
  
  return extensionAttributes.some(attr => 
    document.querySelector(`[${attr}]`) !== null
  );
}

export function suppressHydrationWarnings(): void {
  if (typeof window === 'undefined') return;
  
  // Temporarily suppress hydration warnings in development
  if (process.env.NODE_ENV === 'development') {
    const originalError = console.error;
    console.error = (...args: any[]) => {
      if (
        typeof args[0] === 'string' &&
        args[0].includes('hydration') &&
        args[0].includes('mismatch')
      ) {
        // Suppress hydration mismatch errors caused by browser extensions
        return;
      }
      originalError.apply(console, args);
    };
  }
}

export function cleanupExtensionAttributes(): void {
  if (typeof window === 'undefined') return;
  
  // Remove browser extension attributes after component mount
  setTimeout(() => {
    const extensionAttributes = [
      'bis_skin_checked',
      'webcrx',
      '__processed_0561e652-f089-4c1b-88da-52458fca1d20__',
    ];
    
    extensionAttributes.forEach(attr => {
      const elements = document.querySelectorAll(`[${attr}]`);
      elements.forEach(el => {
        el.removeAttribute(attr);
      });
    });
  }, 100);
}
