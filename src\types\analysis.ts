export interface ImageAnalysisResult {
  text: string;
  detectedComponents: {
    buttons: UIComponent[];
    inputs: UIComponent[];
    navigation: UIComponent[];
    cards: UIComponent[];
  };
  colorPalette: Color[];
  layout: LayoutAnalysis;
}

export interface UIComponent {
  type: string;
  bounds: BoundingBox;
  properties: Record<string, any>;
}

export interface Color {
  hex: string;
  rgb: [number, number, number];
  prominence: number;
}

export interface LayoutAnalysis {
  type: 'grid' | 'flex' | 'custom';
  columns?: number;
  sections: LayoutSection[];
}

export interface LayoutSection {
  id: string;
  type: string;
  children: LayoutSection[];
  bounds: BoundingBox;
}

export interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
} 